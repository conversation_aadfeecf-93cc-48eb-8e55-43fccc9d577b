#!/usr/bin/env python3
"""
AI插件真实场景验证测试

使用真实的API Key进行AI服务验证
"""

import pytest
import sys
import os
import asyncio

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from plugins.ai.smart_ai_loader import SmartAILoader, global_ai_loader
from plugins.ai.ai_factory import AIServiceType
from plugins.ai.ai_config_examples import setup_ai_config_from_env, get_api_key_instructions
from template import create_template_engine
from plugins.plugin_registry import PluginRegistry


class TestAIRealScenario:
    """AI插件真实场景测试类"""
    
    @pytest.fixture(scope="class")
    def ai_config(self):
        """获取AI配置"""
        config = setup_ai_config_from_env()
        if not config or all(cfg.get('enable_mock', False) for cfg in config.values()):
            pytest.skip("未检测到真实的AI API Key，跳过真实场景测试")
        return config
    
    @pytest.fixture
    def ai_loader(self, ai_config):
        """创建配置好的AI加载器"""
        loader = SmartAILoader(enable_debug=True)
        
        # 配置所有可用的提供者
        for provider_name, provider_config in ai_config.items():
            if not provider_config.get('enable_mock', False):
                loader.configure_provider(provider_name, provider_config)
        
        return loader
    
    @pytest.fixture
    def template_engine(self, ai_config):
        """创建配置好的模板引擎"""
        registry = PluginRegistry()
        engine = create_template_engine(registry, debug=True)
        
        # 配置全局AI加载器
        for provider_name, provider_config in ai_config.items():
            if not provider_config.get('enable_mock', False):
                global_ai_loader.configure_provider(provider_name, provider_config)
        
        return engine
    
    def test_openai_text_generation(self, ai_loader, ai_config):
        """测试OpenAI文本生成"""
        if 'openai' not in ai_config:
            pytest.skip("未配置OpenAI API Key")
        
        try:
            result = ai_loader.process_ai_request(
                AIServiceType.TEXT_GENERATION,
                "请用一句话介绍人工智能",
                {
                    'provider': 'openai',
                    'model': 'gpt-3.5-turbo',
                    'max_tokens': 100,
                    'temperature': 0.7
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"OpenAI请求失败: {result.data.get('error')}"
            assert result.data['type'] == 'text_generation'
            assert result.data['provider'] == 'openai'
            assert 'generated_text' in result.data
            assert len(result.data['generated_text']) > 0
            
            print(f"✅ OpenAI文本生成成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   生成文本: {result.data['generated_text']}")
            print(f"   Token使用: {result.data.get('usage', {})}")
            
        except Exception as e:
            pytest.fail(f"OpenAI文本生成测试失败: {e}")
    
    def test_claude_conversation(self, ai_loader, ai_config):
        """测试Claude对话"""
        if 'claude' not in ai_config:
            pytest.skip("未配置Claude API Key")
        
        try:
            messages = [
                {"role": "user", "content": "什么是机器学习？请简要说明。"}
            ]
            
            result = ai_loader.process_ai_request(
                AIServiceType.CONVERSATION,
                messages,
                {
                    'provider': 'claude',
                    'model': 'claude-3-haiku-20240307',  # 使用较便宜的模型
                    'max_tokens': 150
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"Claude请求失败: {result.data.get('error')}"
            assert result.data['type'] == 'conversation'
            assert result.data['provider'] == 'claude'
            assert 'response' in result.data
            assert 'content' in result.data['response']
            
            print(f"✅ Claude对话成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   回复: {result.data['response']['content']}")
            print(f"   Token使用: {result.data.get('usage', {})}")
            
        except Exception as e:
            pytest.fail(f"Claude对话测试失败: {e}")
    
    def test_text_summarization(self, ai_loader, ai_config):
        """测试文本摘要"""
        available_providers = [p for p in ai_config.keys() if not ai_config[p].get('enable_mock', False)]
        if not available_providers:
            pytest.skip("未配置任何真实的AI提供者")
        
        provider = available_providers[0]  # 使用第一个可用的提供者
        
        long_text = """
        人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
        并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
        语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
        应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
        人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、
        也可能超过人的智能。
        """
        
        try:
            result = ai_loader.process_ai_request(
                AIServiceType.SUMMARIZATION,
                long_text.strip(),
                {
                    'provider': provider,
                    'max_tokens': 100
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"文本摘要失败: {result.data.get('error')}"
            assert result.data['type'] == 'summarization'
            assert 'summary' in result.data
            assert len(result.data['summary']) > 0
            
            print(f"✅ 文本摘要成功:")
            print(f"   提供者: {result.data['provider']}")
            print(f"   原文长度: {len(long_text)}")
            print(f"   摘要: {result.data['summary']}")
            print(f"   压缩比: {result.data.get('compression_ratio', 'N/A')}")
            
        except Exception as e:
            pytest.fail(f"文本摘要测试失败: {e}")
    
    def test_template_integration_real(self, template_engine, ai_config):
        """测试模板引擎真实集成"""
        available_providers = [p for p in ai_config.keys() if not ai_config[p].get('enable_mock', False)]
        if not available_providers:
            pytest.skip("未配置任何真实的AI提供者")
        
        provider = available_providers[0]
        
        template = f"""
        {{%- set greeting = sd.ai('text_generation', '用一句话问候用户', provider='{provider}', max_tokens=50) -%}}
        {{%- set analysis = sd.ai('text_analysis', '今天天气很好，适合出门散步', provider='{provider}', max_tokens=80) -%}}
        
        AI服务模板集成测试:
        - 问候语: {{{{ greeting.data.generated_text or greeting.data.response.content or '生成失败' }}}}
        - 分析结果: {{{{ analysis.data.generated_text or analysis.data.response.content or '分析失败' }}}}
        - 使用的提供者: {{{{ greeting.data.provider }}}}
        """
        
        try:
            result = template_engine.render_template(template)
            
            # 验证结果
            assert "AI服务模板集成测试:" in result
            assert provider in result
            assert "生成失败" not in result
            assert "分析失败" not in result
            
            print(f"✅ 模板引擎真实集成成功:")
            print(result.strip())
            
        except Exception as e:
            pytest.fail(f"模板引擎真实集成测试失败: {e}")
    
    def test_async_real_processing(self, ai_loader, ai_config):
        """测试异步真实处理"""
        available_providers = [p for p in ai_config.keys() if not ai_config[p].get('enable_mock', False)]
        if not available_providers:
            pytest.skip("未配置任何真实的AI提供者")
        
        async def async_test():
            provider = available_providers[0]
            
            # 异步处理单个请求
            result = await ai_loader.process_ai_request_async(
                AIServiceType.TEXT_GENERATION,
                "异步测试：请说一句励志的话",
                {'provider': provider, 'max_tokens': 80}
            )
            
            assert 'error' not in result.data
            assert len(result.data.get('generated_text', result.data.get('response', {}).get('content', ''))) > 0
            
            # 异步批量处理
            requests = [
                {
                    'service_type': AIServiceType.TEXT_GENERATION,
                    'data': '生成一个创意标题',
                    'options': {'provider': provider, 'max_tokens': 50}
                },
                {
                    'service_type': AIServiceType.TEXT_GENERATION,
                    'data': '写一句感谢的话',
                    'options': {'provider': provider, 'max_tokens': 50}
                }
            ]
            
            results = await ai_loader.batch_process_async(requests)
            assert len(results) == 2
            
            for i, result in enumerate(results):
                assert 'error' not in result.data, f"批量请求 {i+1} 失败: {result.data.get('error')}"
            
            print(f"✅ 异步真实处理成功:")
            print(f"   单个请求: {result.data.get('generated_text', result.data.get('response', {}).get('content', ''))}")
            print(f"   批量请求: {len(results)} 个请求全部成功")
            
            return True
        
        try:
            success = asyncio.run(async_test())
            assert success
        except Exception as e:
            pytest.fail(f"异步真实处理测试失败: {e}")
    
    def test_cost_estimation(self, ai_loader, ai_config):
        """测试成本估算"""
        if 'openai' not in ai_config:
            pytest.skip("未配置OpenAI API Key")
        
        try:
            # 进行一个简单的请求来获取token使用情况
            result = ai_loader.process_ai_request(
                AIServiceType.TEXT_GENERATION,
                "Hello",
                {
                    'provider': 'openai',
                    'model': 'gpt-3.5-turbo',
                    'max_tokens': 10
                }
            )
            
            if 'usage' in result.data:
                usage = result.data['usage']
                
                # 估算成本 (gpt-3.5-turbo: $0.002/1K tokens)
                total_tokens = usage.get('total_tokens', 0)
                estimated_cost = (total_tokens / 1000) * 0.002
                
                print(f"✅ 成本估算:")
                print(f"   输入tokens: {usage.get('prompt_tokens', 0)}")
                print(f"   输出tokens: {usage.get('completion_tokens', 0)}")
                print(f"   总tokens: {total_tokens}")
                print(f"   估算成本: ${estimated_cost:.6f}")
                
                assert total_tokens > 0, "Token使用量应该大于0"
            else:
                print("⚠️  未返回token使用信息")
                
        except Exception as e:
            pytest.fail(f"成本估算测试失败: {e}")
    
    def test_error_handling_real(self, ai_loader, ai_config):
        """测试真实环境下的错误处理"""
        available_providers = [p for p in ai_config.keys() if not ai_config[p].get('enable_mock', False)]
        if not available_providers:
            pytest.skip("未配置任何真实的AI提供者")
        
        provider = available_providers[0]
        
        # 测试过长的输入
        very_long_text = "测试" * 10000  # 非常长的文本
        
        result = ai_loader.process_ai_request(
            AIServiceType.TEXT_GENERATION,
            very_long_text,
            {'provider': provider, 'max_tokens': 10}
        )
        
        # 应该能处理或返回合理的错误
        if 'error' in result.data:
            print(f"✅ 正确处理了过长输入错误: {result.data['error']}")
        else:
            print(f"✅ 成功处理了长输入")
        
        # 测试无效的模型
        result = ai_loader.process_ai_request(
            AIServiceType.TEXT_GENERATION,
            "测试",
            {'provider': provider, 'model': 'invalid-model-name'}
        )
        
        # 应该返回错误或使用默认模型
        if 'error' in result.data:
            print(f"✅ 正确处理了无效模型错误: {result.data['error']}")
        else:
            print(f"✅ 自动回退到有效模型")


def test_show_api_instructions():
    """显示API Key申请说明"""
    print("\n" + "="*60)
    print("🔑 如果测试被跳过，请按以下说明配置API Key:")
    print("="*60)
    print(get_api_key_instructions())
    print("="*60)


if __name__ == "__main__":
    # 首先显示API Key说明
    test_show_api_instructions()
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
