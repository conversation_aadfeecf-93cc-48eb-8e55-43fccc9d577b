"""
REST插件标准处理器接口实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现IPluginHandler接口
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from core.smart_data_object import SmartDataObject
from core.async_sync_coordinator import AsyncSyncCoordinator


class IRestHandler(ABC):
    """REST处理器接口 - 按照插件标准规范"""
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        pass
    
    @abstractmethod
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理数据"""
        pass
    
    @abstractmethod
    def get_handler_type(self) -> str:
        """获取处理器类型"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass


class BaseRestHandler(IRestHandler):
    """REST处理器基类"""
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self._cache: Dict[str, Any] = {}
        
    def can_handle(self, data: Any) -> bool:
        """基础的数据类型检查"""
        if isinstance(data, str):
            # 检查是否是URL
            try:
                parsed = urlparse(data)
                return bool(parsed.scheme and parsed.netloc)
            except:
                return False
        elif isinstance(data, dict):
            # 检查是否包含URL或REST配置
            return 'url' in data or 'endpoint' in data or 'api' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['url', 'http', 'https', 'api', 'rest', 'endpoint']


class HttpGetHandler(BaseRestHandler):
    """HTTP GET请求处理器"""
    
    def get_handler_type(self) -> str:
        return "http_get"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理GET请求"""
        if not super().can_handle(data):
            return False
        
        if isinstance(data, dict):
            method = data.get('method', 'GET').upper()
            return method == 'GET'
        
        # 对于字符串URL，默认支持GET
        return isinstance(data, str)
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理GET请求"""
        try:
            # 导入REST处理器
            from .rest_processor import RestApiProcessor
            
            # 创建处理器实例
            processor = RestApiProcessor(enable_debug=True)
            
            # 配置处理器
            config = options or {}
            await processor.configure(config)
            await processor.open()
            
            try:
                # 处理请求
                if isinstance(data, str):
                    result = await processor.process(data, {'method': 'GET'})
                else:
                    result = await processor.process(data)
                
                # 转换为SmartDataObject
                return SmartDataObject(result)
                
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"HTTP GET处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class HttpPostHandler(BaseRestHandler):
    """HTTP POST请求处理器"""
    
    def get_handler_type(self) -> str:
        return "http_post"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理POST请求"""
        if not super().can_handle(data):
            return False
        
        if isinstance(data, dict):
            method = data.get('method', 'GET').upper()
            return method == 'POST'
        
        return False
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理POST请求"""
        try:
            from .rest_processor import RestApiProcessor
            
            processor = RestApiProcessor(enable_debug=True)
            
            config = options or {}
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"HTTP POST处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class ApiEndpointHandler(BaseRestHandler):
    """API端点处理器"""
    
    def get_handler_type(self) -> str:
        return "api_endpoint"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理API端点"""
        if isinstance(data, str):
            # 检查URL是否包含API相关路径
            return '/api/' in data.lower() or data.lower().endswith('/api')
        elif isinstance(data, dict):
            # 检查是否包含API配置
            return any(key in data for key in ['api_key', 'endpoint', 'api_url'])
        return False
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理API端点请求"""
        try:
            from .rest_processor import RestApiProcessor
            
            processor = RestApiProcessor(enable_debug=True)
            
            # 为API请求设置特殊配置
            config = options or {}
            config.setdefault('timeout', 60.0)  # API请求通常需要更长超时
            config.setdefault('max_retries', 5)  # API请求重试次数更多
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"API端点处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class RestfulApiHandler(BaseRestHandler):
    """RESTful API处理器"""
    
    def get_handler_type(self) -> str:
        return "restful_api"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理RESTful API"""
        if isinstance(data, dict):
            # 检查是否包含RESTful特征
            method = data.get('method', '').upper()
            restful_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']
            return method in restful_methods and ('url' in data or 'endpoint' in data)
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['rest', 'restful', 'api', 'json_api', 'resource']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理RESTful API请求"""
        try:
            from .rest_processor import RestApiProcessor
            
            processor = RestApiProcessor(enable_debug=True)
            
            # RESTful API特殊配置
            config = options or {}
            config.setdefault('enable_cache', True)
            config.setdefault('cache_ttl', 300)  # 5分钟缓存
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"RESTful API处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class GraphQLHandler(BaseRestHandler):
    """GraphQL处理器"""
    
    def get_handler_type(self) -> str:
        return "graphql"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理GraphQL"""
        if isinstance(data, dict):
            # 检查是否包含GraphQL查询
            return 'query' in data or 'mutation' in data or 'subscription' in data
        elif isinstance(data, str):
            # 检查URL是否是GraphQL端点
            return '/graphql' in data.lower()
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['graphql', 'gql', 'query', 'mutation', 'subscription']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理GraphQL请求"""
        try:
            from .rest_processor import RestApiProcessor
            
            processor = RestApiProcessor(enable_debug=True)
            
            # GraphQL特殊配置
            config = options or {}
            config.setdefault('headers', {})
            config['headers'].setdefault('Content-Type', 'application/json')
            
            await processor.configure(config)
            await processor.open()
            
            try:
                # 如果是字符串URL，转换为POST请求
                if isinstance(data, str):
                    query = options.get('query', '')
                    variables = options.get('variables', {})
                    
                    request_data = {
                        'url': data,
                        'method': 'POST',
                        'json': {
                            'query': query,
                            'variables': variables
                        }
                    }
                    result = await processor.process(request_data)
                else:
                    result = await processor.process(data)
                
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"GraphQL处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })
