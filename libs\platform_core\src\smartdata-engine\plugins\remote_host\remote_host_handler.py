"""
Remote Host插件标准处理器接口实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现IPluginHandler接口
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union

from core.smart_data_object import SmartDataObject
from core.async_sync_coordinator import AsyncSyncCoordinator


class IRemoteHostHandler(ABC):
    """Remote Host处理器接口 - 按照插件标准规范"""
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        pass
    
    @abstractmethod
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理数据"""
        pass
    
    @abstractmethod
    def get_handler_type(self) -> str:
        """获取处理器类型"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass


class BaseRemoteHostHandler(IRemoteHostHandler):
    """Remote Host处理器基类"""
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self._cache: Dict[str, Any] = {}
        
    def can_handle(self, data: Any) -> bool:
        """基础的数据类型检查"""
        if isinstance(data, str):
            # 检查是否是SSH连接字符串
            return 'ssh://' in data or '@' in data
        elif isinstance(data, dict):
            # 检查是否包含远程主机配置
            return any(key in data for key in ['host', 'hostname', 'server', 'ssh_config'])
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['ssh', 'remote', 'host', 'server', 'command']


class SSHConnectionHandler(BaseRemoteHostHandler):
    """SSH连接处理器"""
    
    def get_handler_type(self) -> str:
        return "ssh_connection"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理SSH连接"""
        if isinstance(data, str):
            return 'ssh://' in data or ('@' in data and ':' in data)
        elif isinstance(data, dict):
            return 'host' in data and ('username' in data or 'user' in data)
        return False
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理SSH连接"""
        try:
            from .remote_host_processor import RemoteHostProcessor
            
            processor = RemoteHostProcessor(enable_debug=True)
            
            # 配置处理器
            config = options or {}
            await processor.configure(config)
            await processor.open()
            
            try:
                # 处理连接请求
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"SSH连接处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class CommandExecutionHandler(BaseRemoteHostHandler):
    """命令执行处理器"""
    
    def get_handler_type(self) -> str:
        return "command_execution"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理命令执行"""
        if isinstance(data, dict):
            return 'command' in data or 'cmd' in data or 'script' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['command', 'cmd', 'script', 'shell', 'bash']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理命令执行"""
        try:
            from .remote_host_processor import RemoteHostProcessor
            
            processor = RemoteHostProcessor(enable_debug=True)
            
            config = options or {}
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"命令执行处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class FileTransferHandler(BaseRemoteHostHandler):
    """文件传输处理器"""
    
    def get_handler_type(self) -> str:
        return "file_transfer"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理文件传输"""
        if isinstance(data, dict):
            return any(key in data for key in ['upload', 'download', 'sync', 'sftp'])
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['sftp', 'scp', 'upload', 'download', 'sync', 'file_transfer']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理文件传输"""
        try:
            from .remote_host_processor import RemoteHostProcessor
            
            processor = RemoteHostProcessor(enable_debug=True)
            
            # 文件传输特殊配置
            config = options or {}
            config.setdefault('enable_compression', True)
            config.setdefault('preserve_permissions', True)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"文件传输处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class SystemMonitorHandler(BaseRemoteHostHandler):
    """系统监控处理器"""
    
    def get_handler_type(self) -> str:
        return "system_monitor"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理系统监控"""
        if isinstance(data, dict):
            return any(key in data for key in ['monitor', 'stats', 'system_info', 'performance'])
        elif isinstance(data, str):
            monitor_commands = ['top', 'ps', 'df', 'free', 'netstat', 'iostat']
            return any(cmd in data.lower() for cmd in monitor_commands)
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['monitor', 'stats', 'system', 'performance', 'metrics']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理系统监控"""
        try:
            from .remote_host_processor import RemoteHostProcessor
            
            processor = RemoteHostProcessor(enable_debug=True)
            
            # 系统监控特殊配置
            config = options or {}
            config.setdefault('timeout', 60.0)  # 监控命令可能需要更长时间
            config.setdefault('enable_monitoring', True)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"系统监控处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class TunnelHandler(BaseRemoteHostHandler):
    """隧道处理器"""
    
    def get_handler_type(self) -> str:
        return "tunnel"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理隧道"""
        if isinstance(data, dict):
            return any(key in data for key in ['tunnel', 'port_forward', 'proxy'])
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['tunnel', 'port_forward', 'proxy', 'socks', 'local_forward', 'remote_forward']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理隧道"""
        try:
            from .remote_host_processor import RemoteHostProcessor
            
            processor = RemoteHostProcessor(enable_debug=True)
            
            # 隧道特殊配置
            config = options or {}
            config.setdefault('keep_alive', True)
            config.setdefault('auto_reconnect', True)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"隧道处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })
