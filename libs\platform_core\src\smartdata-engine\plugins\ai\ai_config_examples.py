"""
AI插件真实配置示例

提供可用的AI服务配置示例，包括免费和付费选项
"""

import os
from typing import Dict, Any


class AIConfigExamples:
    """AI配置示例类"""
    
    @staticmethod
    def get_openai_config() -> Dict[str, Any]:
        """获取OpenAI配置 - 需要付费API Key"""
        return {
            'provider': 'openai',
            'config': {
                'api_key': os.getenv('OPENAI_API_KEY', 'sk-your-openai-api-key-here'),
                'base_url': 'https://api.openai.com/v1',
                'organization': os.getenv('OPENAI_ORG_ID', None),  # 可选
            },
            'default_model': 'gpt-3.5-turbo',
            'available_models': [
                'gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo',
                'text-embedding-ada-002', 'text-davinci-003'
            ],
            'pricing_info': {
                'gpt-3.5-turbo': '$0.002/1K tokens',
                'gpt-4': '$0.03/1K tokens',
                'text-embedding-ada-002': '$0.0001/1K tokens'
            }
        }
    
    @staticmethod
    def get_claude_config() -> Dict[str, Any]:
        """获取Claude配置 - 需要付费API Key"""
        return {
            'provider': 'claude',
            'config': {
                'api_key': os.getenv('ANTHROPIC_API_KEY', 'sk-ant-your-anthropic-api-key-here'),
                'base_url': 'https://api.anthropic.com',
            },
            'default_model': 'claude-3-sonnet-20240229',
            'available_models': [
                'claude-3-opus-20240229',
                'claude-3-sonnet-20240229', 
                'claude-3-haiku-20240307',
                'claude-2.1',
                'claude-2.0',
                'claude-instant-1.2'
            ],
            'pricing_info': {
                'claude-3-haiku': '$0.25/$1.25 per MTok (input/output)',
                'claude-3-sonnet': '$3/$15 per MTok (input/output)',
                'claude-3-opus': '$15/$75 per MTok (input/output)'
            }
        }
    
    @staticmethod
    def get_azure_openai_config() -> Dict[str, Any]:
        """获取Azure OpenAI配置 - 企业级选项"""
        return {
            'provider': 'azure_openai',
            'config': {
                'api_key': os.getenv('AZURE_OPENAI_API_KEY', 'your-azure-openai-key'),
                'base_url': os.getenv('AZURE_OPENAI_ENDPOINT', 'https://your-resource.openai.azure.com/'),
                'api_version': '2024-02-15-preview',
                'deployment_name': os.getenv('AZURE_OPENAI_DEPLOYMENT', 'gpt-35-turbo')
            },
            'default_model': 'gpt-35-turbo',
            'note': 'Azure OpenAI提供企业级安全和合规性'
        }
    
    @staticmethod
    def get_free_alternatives() -> Dict[str, Any]:
        """获取免费/开源替代方案"""
        return {
            'huggingface_free': {
                'provider': 'huggingface',
                'config': {
                    'api_key': 'hf_your-huggingface-token',  # 免费注册获取
                    'base_url': 'https://api-inference.huggingface.co/models/'
                },
                'free_models': [
                    'microsoft/DialoGPT-medium',
                    'facebook/blenderbot-400M-distill',
                    'google/flan-t5-base'
                ],
                'note': '免费但有速率限制'
            },
            'ollama_local': {
                'provider': 'ollama',
                'config': {
                    'base_url': 'http://localhost:11434',
                    'api_key': None  # 本地运行无需API Key
                },
                'local_models': [
                    'llama2', 'codellama', 'mistral', 'neural-chat'
                ],
                'note': '完全免费，需要本地安装Ollama'
            },
            'openai_compatible': {
                'provider': 'openai_compatible',
                'config': {
                    'api_key': 'your-api-key',
                    'base_url': 'http://localhost:8000/v1'  # 本地兼容服务
                },
                'note': '兼容OpenAI API的本地服务'
            }
        }
    
    @staticmethod
    def get_demo_config() -> Dict[str, Any]:
        """获取演示配置 - 使用模拟响应"""
        return {
            'provider': 'demo',
            'config': {
                'api_key': 'demo-key',
                'base_url': 'https://demo.api.com',
                'enable_mock': True  # 启用模拟响应
            },
            'default_model': 'demo-model',
            'note': '演示模式，返回模拟响应，无需真实API Key'
        }


def setup_ai_config_from_env() -> Dict[str, Any]:
    """从环境变量设置AI配置"""
    config = {}
    
    # OpenAI配置
    if os.getenv('OPENAI_API_KEY'):
        config['openai'] = {
            'api_key': os.getenv('OPENAI_API_KEY'),
            'base_url': os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1'),
            'organization': os.getenv('OPENAI_ORG_ID')
        }
        print("✅ 检测到OpenAI API Key配置")
    
    # Claude配置
    if os.getenv('ANTHROPIC_API_KEY'):
        config['claude'] = {
            'api_key': os.getenv('ANTHROPIC_API_KEY'),
            'base_url': os.getenv('ANTHROPIC_BASE_URL', 'https://api.anthropic.com')
        }
        print("✅ 检测到Claude API Key配置")
    
    # Azure OpenAI配置
    if os.getenv('AZURE_OPENAI_API_KEY'):
        config['azure_openai'] = {
            'api_key': os.getenv('AZURE_OPENAI_API_KEY'),
            'base_url': os.getenv('AZURE_OPENAI_ENDPOINT'),
            'api_version': os.getenv('AZURE_OPENAI_API_VERSION', '2024-02-15-preview'),
            'deployment_name': os.getenv('AZURE_OPENAI_DEPLOYMENT')
        }
        print("✅ 检测到Azure OpenAI配置")
    
    # 如果没有配置，使用演示模式
    if not config:
        config['demo'] = {
            'api_key': 'demo-key',
            'enable_mock': True
        }
        print("⚠️  未检测到API Key，使用演示模式")
    
    return config


def get_api_key_instructions() -> str:
    """获取API Key申请说明"""
    return """
# 🔑 AI服务API Key申请指南

## 1. OpenAI API Key (推荐)
- 访问: https://platform.openai.com/api-keys
- 注册账户并验证手机号
- 创建API Key
- 设置环境变量: OPENAI_API_KEY=sk-your-key-here
- 费用: 按使用量付费，新用户有免费额度

## 2. Claude API Key (Anthropic)
- 访问: https://console.anthropic.com/
- 注册账户
- 创建API Key
- 设置环境变量: ANTHROPIC_API_KEY=sk-ant-your-key-here
- 费用: 按使用量付费

## 3. 免费替代方案

### Hugging Face (免费)
- 访问: https://huggingface.co/settings/tokens
- 注册账户
- 创建Access Token
- 设置环境变量: HF_TOKEN=hf_your-token-here
- 限制: 有速率限制，模型质量较低

### Ollama (完全免费)
- 访问: https://ollama.ai/
- 下载并安装Ollama
- 运行: ollama run llama2
- 无需API Key，完全本地运行

## 4. 环境变量设置

### Windows
```cmd
set OPENAI_API_KEY=sk-your-key-here
set ANTHROPIC_API_KEY=sk-ant-your-key-here
```

### Linux/Mac
```bash
export OPENAI_API_KEY=sk-your-key-here
export ANTHROPIC_API_KEY=sk-ant-your-key-here
```

### Python代码中设置
```python
import os
os.environ['OPENAI_API_KEY'] = 'sk-your-key-here'
os.environ['ANTHROPIC_API_KEY'] = 'sk-ant-your-key-here'
```
"""


if __name__ == "__main__":
    # 显示配置示例
    print("🤖 AI插件配置示例")
    print("=" * 50)
    
    # 显示API Key申请指南
    print(get_api_key_instructions())
    
    # 检查当前环境配置
    print("\n📋 当前环境配置检查:")
    config = setup_ai_config_from_env()
    
    if config:
        print(f"✅ 已配置 {len(config)} 个AI服务提供者")
        for provider in config.keys():
            print(f"   - {provider}")
    else:
        print("❌ 未检测到任何AI服务配置")
    
    # 显示免费替代方案
    print("\n🆓 免费替代方案:")
    free_alternatives = AIConfigExamples.get_free_alternatives()
    for name, info in free_alternatives.items():
        print(f"   - {name}: {info['note']}")
