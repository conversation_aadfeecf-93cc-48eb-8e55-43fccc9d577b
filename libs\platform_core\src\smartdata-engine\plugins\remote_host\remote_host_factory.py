"""
Remote Host插件工厂模式实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现工厂模式
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union

from .remote_host_handler import (
    IRemoteHostHandler, BaseRemoteHostHandler, SSHConnectionHandler,
    CommandExecutionHandler, FileTransferHandler, SystemMonitorHandler, TunnelHandler
)


class RemoteHostHandlerFactory:
    """Remote Host处理器工厂 - 按照插件标准规范"""
    
    # 注册的处理器类型
    _handlers: Dict[str, Type[IRemoteHostHandler]] = {
        'ssh_connection': SSHConnectionHandler,
        'command_execution': CommandExecutionHandler,
        'file_transfer': FileTransferHandler,
        'system_monitor': SystemMonitorHandler,
        'tunnel': TunnelHandler,
    }
    
    # 处理器优先级（数字越大优先级越高）
    _handler_priorities: Dict[str, int] = {
        'tunnel': 90,              # 隧道最高优先级
        'file_transfer': 80,       # 文件传输次之
        'system_monitor': 70,      # 系统监控
        'command_execution': 60,   # 命令执行
        'ssh_connection': 50,      # SSH连接最低优先级
    }
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.RemoteHostHandlerFactory")
        self.enable_debug = enable_debug
        self._handler_cache: Dict[str, IRemoteHostHandler] = {}
    
    @classmethod
    def register_handler(cls, handler_type: str, handler_class: Type[IRemoteHostHandler], priority: int = 50):
        """注册新的处理器类型"""
        cls._handlers[handler_type] = handler_class
        cls._handler_priorities[handler_type] = priority
        logging.getLogger(__name__).info(f"注册Remote Host处理器: {handler_type} (优先级: {priority})")
    
    @classmethod
    def unregister_handler(cls, handler_type: str):
        """注销处理器类型"""
        if handler_type in cls._handlers:
            del cls._handlers[handler_type]
            cls._handler_priorities.pop(handler_type, None)
            logging.getLogger(__name__).info(f"注销Remote Host处理器: {handler_type}")
    
    @classmethod
    def get_registered_handlers(cls) -> Dict[str, Type[IRemoteHostHandler]]:
        """获取所有注册的处理器"""
        return cls._handlers.copy()
    
    def detect_handler_type(self, data: Any) -> Optional[str]:
        """智能检测最适合的处理器类型"""
        try:
            # 按优先级排序处理器
            sorted_handlers = sorted(
                self._handlers.items(),
                key=lambda x: self._handler_priorities.get(x[0], 0),
                reverse=True
            )
            
            # 逐个检查处理器是否可以处理数据
            for handler_type, handler_class in sorted_handlers:
                try:
                    # 创建临时实例进行检测
                    temp_handler = handler_class(enable_debug=False)
                    if temp_handler.can_handle(data):
                        self.logger.debug(f"检测到处理器类型: {handler_type}")
                        return handler_type
                except Exception as e:
                    self.logger.debug(f"处理器 {handler_type} 检测失败: {e}")
                    continue
            
            self.logger.warning(f"未找到合适的处理器类型: {type(data)}")
            return None
            
        except Exception as e:
            self.logger.error(f"处理器类型检测失败: {e}")
            return None
    
    def create_handler(self, handler_type: str) -> Optional[IRemoteHostHandler]:
        """创建指定类型的处理器"""
        try:
            if handler_type not in self._handlers:
                self.logger.error(f"未知的处理器类型: {handler_type}")
                return None
            
            # 检查缓存
            if handler_type in self._handler_cache:
                return self._handler_cache[handler_type]
            
            # 创建新的处理器实例
            handler_class = self._handlers[handler_type]
            handler = handler_class(enable_debug=self.enable_debug)
            
            # 缓存处理器实例
            self._handler_cache[handler_type] = handler
            
            self.logger.debug(f"创建Remote Host处理器: {handler_type}")
            return handler
            
        except Exception as e:
            self.logger.error(f"创建处理器失败: {handler_type}, 错误: {e}")
            return None
    
    def create_best_handler(self, data: Any) -> Optional[IRemoteHostHandler]:
        """创建最适合的处理器"""
        handler_type = self.detect_handler_type(data)
        if handler_type:
            return self.create_handler(handler_type)
        return None
    
    def get_handler_info(self, handler_type: str) -> Optional[Dict[str, Any]]:
        """获取处理器信息"""
        try:
            if handler_type not in self._handlers:
                return None
            
            handler_class = self._handlers[handler_type]
            temp_handler = handler_class(enable_debug=False)
            
            return {
                'type': handler_type,
                'class_name': handler_class.__name__,
                'supported_types': temp_handler.get_supported_types(),
                'priority': self._handler_priorities.get(handler_type, 0),
                'description': handler_class.__doc__ or f"{handler_type} 处理器"
            }
            
        except Exception as e:
            self.logger.error(f"获取处理器信息失败: {handler_type}, 错误: {e}")
            return None
    
    def list_all_handlers(self) -> List[Dict[str, Any]]:
        """列出所有处理器信息"""
        handlers_info = []
        
        for handler_type in self._handlers:
            info = self.get_handler_info(handler_type)
            if info:
                handlers_info.append(info)
        
        # 按优先级排序
        handlers_info.sort(key=lambda x: x['priority'], reverse=True)
        return handlers_info
    
    def clear_cache(self):
        """清理处理器缓存"""
        self._handler_cache.clear()
        self.logger.debug("Remote Host处理器缓存已清理")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取工厂统计信息"""
        return {
            'registered_handlers': len(self._handlers),
            'cached_handlers': len(self._handler_cache),
            'handler_types': list(self._handlers.keys()),
            'priorities': self._handler_priorities.copy()
        }


class RemoteHostConnectionFactory:
    """Remote Host连接工厂"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.RemoteHostConnectionFactory")
        self._connection_pool: Dict[str, Any] = {}
    
    def create_connection(self, config: Dict[str, Any]) -> Optional[Any]:
        """创建Remote Host连接"""
        try:
            connection_key = self._generate_connection_key(config)
            
            # 检查连接池
            if connection_key in self._connection_pool:
                return self._connection_pool[connection_key]
            
            # 创建新连接（这里使用asyncssh作为示例）
            try:
                import asyncssh
                
                connection_config = {
                    'host': config['host'],
                    'port': config.get('port', 22),
                    'username': config['username'],
                    'timeout': config.get('timeout', 30.0),
                    'keepalive_interval': config.get('keepalive_interval', 30.0),
                    'compression_algs': ['zlib'] if config.get('compression', True) else None,
                }
                
                # 添加认证信息
                if 'password' in config:
                    connection_config['password'] = config['password']
                elif 'private_key' in config:
                    connection_config['client_keys'] = [config['private_key']]
                
                # 这里返回配置，实际连接在使用时建立
                self._connection_pool[connection_key] = connection_config
                
                self.logger.debug(f"创建Remote Host连接配置: {connection_key}")
                return connection_config
                
            except ImportError:
                self.logger.warning("asyncssh未安装，无法创建连接")
                return None
            
        except Exception as e:
            self.logger.error(f"创建Remote Host连接失败: {e}")
            return None
    
    def _generate_connection_key(self, config: Dict[str, Any]) -> str:
        """生成连接键"""
        key_parts = [
            config.get('host', ''),
            str(config.get('port', 22)),
            config.get('username', ''),
        ]
        return '|'.join(key_parts)
    
    async def close_all_connections(self):
        """关闭所有连接"""
        for connection_key, connection in self._connection_pool.items():
            try:
                # 如果是实际的连接对象，关闭它
                if hasattr(connection, 'close'):
                    await connection.close()
                self.logger.debug(f"关闭Remote Host连接: {connection_key}")
            except Exception as e:
                self.logger.error(f"关闭连接失败: {connection_key}, 错误: {e}")
        
        self._connection_pool.clear()


# 全局工厂实例
remote_host_handler_factory = RemoteHostHandlerFactory()
remote_host_connection_factory = RemoteHostConnectionFactory()


def get_remote_host_handler_factory() -> RemoteHostHandlerFactory:
    """获取Remote Host处理器工厂实例"""
    return remote_host_handler_factory


def get_remote_host_connection_factory() -> RemoteHostConnectionFactory:
    """获取Remote Host连接工厂实例"""
    return remote_host_connection_factory
