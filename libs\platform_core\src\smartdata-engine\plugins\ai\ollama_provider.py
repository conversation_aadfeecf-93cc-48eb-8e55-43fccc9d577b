"""
Ollama本地大模型提供者

支持本地运行的Ollama模型，如gemma3:4b等
"""

import logging
import asyncio
import aiohttp
import json
from typing import Any, Dict, List, Optional

from .ai_factory import IAIServiceProvider, AIServiceType
from core.smart_data_object import SmartDataObject


class OllamaProvider(IAIServiceProvider):
    """Ollama本地大模型提供者"""
    
    def __init__(self, base_url: str = "http://localhost:11434", default_model: str = "gemma3:4b"):
        self.base_url = base_url.rstrip('/')
        self.default_model = default_model
        self.logger = logging.getLogger(f"{__name__}.OllamaProvider")
        
        self.supported_services = [
            AIServiceType.TEXT_GENERATION,
            AIServiceType.CONVERSATION,
            AIServiceType.TEXT_ANALYSIS,
            AIServiceType.SUMMARIZATION,
            AIServiceType.CLASSIFICATION
        ]
        
        # 常见的Ollama模型
        self.available_models = [
            "gemma3:4b", "gemma3:8b", "gemma3:2b",
            "llama3:8b", "llama3:70b", "llama2:7b", "llama2:13b",
            "mistral:7b", "mixtral:8x7b",
            "codellama:7b", "codellama:13b",
            "neural-chat:7b", "starling-lm:7b",
            "phi3:3.8b", "qwen:7b", "deepseek-coder:6.7b"
        ]
    
    def can_handle(self, service_type: str, model: str = None) -> bool:
        """检查是否可以处理指定服务"""
        return service_type in self.supported_services
    
    async def process(self, service_type: str, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理Ollama请求"""
        try:
            options = options or {}
            model = options.get('model', self.default_model)
            
            self.logger.info(f"Ollama处理请求: {service_type}, 模型: {model}")
            
            if service_type == AIServiceType.TEXT_GENERATION:
                result = await self._generate_text(data, model, options)
            elif service_type == AIServiceType.CONVERSATION:
                result = await self._conversation(data, model, options)
            elif service_type == AIServiceType.TEXT_ANALYSIS:
                result = await self._analyze_text(data, model, options)
            elif service_type == AIServiceType.SUMMARIZATION:
                result = await self._summarize(data, model, options)
            elif service_type == AIServiceType.CLASSIFICATION:
                result = await self._classify_text(data, model, options)
            else:
                result = {
                    'error': f'不支持的服务类型: {service_type}',
                    'provider': 'ollama'
                }
            
            return SmartDataObject(result)
            
        except Exception as e:
            self.logger.error(f"Ollama处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'provider': 'ollama',
                'service_type': service_type,
                'model': options.get('model', self.default_model)
            })
    
    async def _make_request(self, endpoint: str, payload: Dict) -> Dict:
        """发送请求到Ollama API"""
        url = f"{self.base_url}/api/{endpoint}"
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload, timeout=aiohttp.ClientTimeout(total=60)) as response:
                    if response.status == 200:
                        # Ollama返回的是流式响应，我们需要读取完整响应
                        full_response = ""
                        async for line in response.content:
                            if line:
                                try:
                                    chunk = json.loads(line.decode('utf-8'))
                                    if 'response' in chunk:
                                        full_response += chunk['response']
                                    if chunk.get('done', False):
                                        return {
                                            'response': full_response,
                                            'model': chunk.get('model', ''),
                                            'created_at': chunk.get('created_at', ''),
                                            'done': True
                                        }
                                except json.JSONDecodeError:
                                    continue
                        
                        return {'response': full_response, 'done': True}
                    else:
                        error_text = await response.text()
                        raise Exception(f"Ollama API错误 {response.status}: {error_text}")
                        
        except aiohttp.ClientError as e:
            raise Exception(f"连接Ollama失败: {e}")
        except asyncio.TimeoutError:
            raise Exception("Ollama请求超时")
    
    async def _generate_text(self, prompt: str, model: str, options: Dict) -> Dict:
        """生成文本"""
        payload = {
            'model': model,
            'prompt': prompt,
            'stream': True,
            'options': {
                'temperature': options.get('temperature', 0.7),
                'top_p': options.get('top_p', 0.9),
                'top_k': options.get('top_k', 40),
                'num_predict': options.get('max_tokens', 500)
            }
        }
        
        response = await self._make_request('generate', payload)
        
        return {
            'type': 'text_generation',
            'provider': 'ollama',
            'model': model,
            'prompt': prompt,
            'generated_text': response['response'],
            'created_at': response.get('created_at', ''),
            'usage': {
                'prompt_tokens': len(prompt.split()),
                'completion_tokens': len(response['response'].split()),
                'total_tokens': len(prompt.split()) + len(response['response'].split())
            }
        }
    
    async def _conversation(self, messages: List[Dict], model: str, options: Dict) -> Dict:
        """对话处理"""
        if not isinstance(messages, list):
            messages = [{"role": "user", "content": str(messages)}]
        
        # 构建对话提示
        conversation_prompt = ""
        for msg in messages:
            role = msg.get('role', 'user')
            content = msg.get('content', '')
            if role == 'user':
                conversation_prompt += f"用户: {content}\n"
            elif role == 'assistant':
                conversation_prompt += f"助手: {content}\n"
            elif role == 'system':
                conversation_prompt += f"系统: {content}\n"
        
        conversation_prompt += "助手: "
        
        payload = {
            'model': model,
            'prompt': conversation_prompt,
            'stream': True,
            'options': {
                'temperature': options.get('temperature', 0.7),
                'num_predict': options.get('max_tokens', 300)
            }
        }
        
        response = await self._make_request('generate', payload)
        
        return {
            'type': 'conversation',
            'provider': 'ollama',
            'model': model,
            'messages': messages,
            'response': {
                'role': 'assistant',
                'content': response['response']
            },
            'created_at': response.get('created_at', ''),
            'usage': {
                'prompt_tokens': len(conversation_prompt.split()),
                'completion_tokens': len(response['response'].split()),
                'total_tokens': len(conversation_prompt.split()) + len(response['response'].split())
            }
        }
    
    async def _analyze_text(self, text: str, model: str, options: Dict) -> Dict:
        """文本分析"""
        analysis_prompt = f"""请分析以下文本的内容，包括主题、情感倾向、关键信息等：

文本内容：
{text}

请提供简洁的分析结果："""
        
        payload = {
            'model': model,
            'prompt': analysis_prompt,
            'stream': True,
            'options': {
                'temperature': 0.3,  # 分析任务使用较低温度
                'num_predict': options.get('max_tokens', 200)
            }
        }
        
        response = await self._make_request('generate', payload)
        
        return {
            'type': 'text_analysis',
            'provider': 'ollama',
            'model': model,
            'text': text,
            'analysis': response['response'],
            'created_at': response.get('created_at', ''),
            'usage': {
                'prompt_tokens': len(analysis_prompt.split()),
                'completion_tokens': len(response['response'].split()),
                'total_tokens': len(analysis_prompt.split()) + len(response['response'].split())
            }
        }
    
    async def _summarize(self, text: str, model: str, options: Dict) -> Dict:
        """文本摘要"""
        summary_prompt = f"""请对以下文本进行摘要，提取主要信息和关键点：

原文：
{text}

摘要："""
        
        payload = {
            'model': model,
            'prompt': summary_prompt,
            'stream': True,
            'options': {
                'temperature': 0.3,
                'num_predict': options.get('max_tokens', 150)
            }
        }
        
        response = await self._make_request('generate', payload)
        
        return {
            'type': 'summarization',
            'provider': 'ollama',
            'model': model,
            'original_text': text,
            'summary': response['response'],
            'compression_ratio': len(response['response']) / len(text) if text else 0,
            'created_at': response.get('created_at', ''),
            'usage': {
                'prompt_tokens': len(summary_prompt.split()),
                'completion_tokens': len(response['response'].split()),
                'total_tokens': len(summary_prompt.split()) + len(response['response'].split())
            }
        }
    
    async def _classify_text(self, text: str, model: str, options: Dict) -> Dict:
        """文本分类"""
        categories = options.get('categories', ['正面', '负面', '中性'])
        
        classify_prompt = f"""请将以下文本分类到这些类别中：{', '.join(categories)}

文本：
{text}

分类结果："""
        
        payload = {
            'model': model,
            'prompt': classify_prompt,
            'stream': True,
            'options': {
                'temperature': 0.1,  # 分类任务使用很低温度
                'num_predict': 50
            }
        }
        
        response = await self._make_request('generate', payload)
        
        return {
            'type': 'classification',
            'provider': 'ollama',
            'model': model,
            'text': text,
            'categories': categories,
            'classification': response['response'],
            'created_at': response.get('created_at', ''),
            'usage': {
                'prompt_tokens': len(classify_prompt.split()),
                'completion_tokens': len(response['response'].split()),
                'total_tokens': len(classify_prompt.split()) + len(response['response'].split())
            }
        }
    
    async def check_model_availability(self, model: str = None) -> bool:
        """检查模型是否可用"""
        try:
            url = f"{self.base_url}/api/tags"
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [m['name'] for m in data.get('models', [])]
                        
                        if model:
                            return model in models
                        else:
                            return len(models) > 0
                    return False
        except Exception as e:
            self.logger.error(f"检查Ollama模型可用性失败: {e}")
            return False
    
    async def list_available_models(self) -> List[str]:
        """列出可用的模型"""
        try:
            url = f"{self.base_url}/api/tags"
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return [m['name'] for m in data.get('models', [])]
                    return []
        except Exception as e:
            self.logger.error(f"获取Ollama模型列表失败: {e}")
            return []
    
    def get_provider_name(self) -> str:
        return "ollama"
    
    def get_supported_services(self) -> List[str]:
        return self.supported_services.copy()
    
    def get_available_models(self) -> List[str]:
        return self.available_models.copy()
