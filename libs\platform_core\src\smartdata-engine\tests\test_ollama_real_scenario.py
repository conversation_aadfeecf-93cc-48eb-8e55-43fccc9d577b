#!/usr/bin/env python3
"""
Ollama本地大模型真实场景验证测试

使用本地Ollama gemma3:4b模型进行AI服务验证
"""

import pytest
import sys
import os
import asyncio
import aiohttp

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from plugins.ai.smart_ai_loader import SmartAILoader, global_ai_loader
from plugins.ai.ai_factory import AIServiceType
from plugins.ai.ollama_provider import OllamaProvider
from template import create_template_engine
from plugins.plugin_registry import PluginRegistry


class TestOllamaRealScenario:
    """Ollama真实场景测试类"""
    
    @pytest.fixture(scope="class")
    async def ollama_check(self):
        """检查Ollama服务是否可用"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get('http://localhost:11434/api/tags', timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        models = [m['name'] for m in data.get('models', [])]
                        
                        if not models:
                            pytest.skip("Ollama服务运行中但没有安装模型，请运行: ollama pull gemma3:4b")
                        
                        # 检查是否有gemma3:4b模型
                        if 'gemma3:4b' not in models:
                            print(f"可用模型: {models}")
                            if models:
                                # 使用第一个可用模型
                                return models[0]
                            else:
                                pytest.skip("没有可用的模型")
                        
                        return 'gemma3:4b'
                    else:
                        pytest.skip("Ollama服务不可用，请确保Ollama正在运行")
        except Exception as e:
            pytest.skip(f"无法连接到Ollama服务: {e}。请确保Ollama正在运行 (ollama serve)")
    
    @pytest.fixture
    def ollama_provider(self, ollama_check):
        """创建Ollama提供者"""
        model = asyncio.run(ollama_check)
        return OllamaProvider(default_model=model)
    
    @pytest.fixture
    def ai_loader(self, ollama_check):
        """创建配置好的AI加载器"""
        model = asyncio.run(ollama_check)
        loader = SmartAILoader(enable_debug=True)
        
        # 配置Ollama提供者
        loader.configure_provider('ollama', {
            'base_url': 'http://localhost:11434',
            'default_model': model
        })
        
        return loader
    
    @pytest.fixture
    def template_engine(self, ollama_check):
        """创建配置好的模板引擎"""
        model = asyncio.run(ollama_check)
        registry = PluginRegistry()
        engine = create_template_engine(registry, debug=True)
        
        # 配置全局AI加载器
        global_ai_loader.configure_provider('ollama', {
            'base_url': 'http://localhost:11434',
            'default_model': model
        })
        
        return engine
    
    def test_ollama_connection(self, ollama_provider):
        """测试Ollama连接"""
        async def check_connection():
            # 检查模型可用性
            available = await ollama_provider.check_model_availability()
            assert available, "Ollama服务不可用"
            
            # 获取模型列表
            models = await ollama_provider.list_available_models()
            assert len(models) > 0, "没有可用的模型"
            
            print(f"✅ Ollama连接成功")
            print(f"   可用模型: {models}")
            
            return True
        
        success = asyncio.run(check_connection())
        assert success
    
    def test_ollama_text_generation(self, ai_loader):
        """测试Ollama文本生成"""
        try:
            result = ai_loader.process_ai_request(
                AIServiceType.TEXT_GENERATION,
                "请用一句话介绍人工智能的定义",
                {
                    'provider': 'ollama',
                    'temperature': 0.7,
                    'max_tokens': 100
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"Ollama文本生成失败: {result.data.get('error')}"
            assert result.data['type'] == 'text_generation'
            assert result.data['provider'] == 'ollama'
            assert 'generated_text' in result.data
            assert len(result.data['generated_text']) > 0
            
            print(f"✅ Ollama文本生成成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   生成文本: {result.data['generated_text']}")
            print(f"   Token使用: {result.data.get('usage', {})}")
            
        except Exception as e:
            pytest.fail(f"Ollama文本生成测试失败: {e}")
    
    def test_ollama_conversation(self, ai_loader):
        """测试Ollama对话"""
        try:
            messages = [
                {"role": "user", "content": "你好，请简单介绍一下你自己"}
            ]
            
            result = ai_loader.process_ai_request(
                AIServiceType.CONVERSATION,
                messages,
                {
                    'provider': 'ollama',
                    'temperature': 0.8,
                    'max_tokens': 150
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"Ollama对话失败: {result.data.get('error')}"
            assert result.data['type'] == 'conversation'
            assert result.data['provider'] == 'ollama'
            assert 'response' in result.data
            assert 'content' in result.data['response']
            
            print(f"✅ Ollama对话成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   回复: {result.data['response']['content']}")
            print(f"   Token使用: {result.data.get('usage', {})}")
            
        except Exception as e:
            pytest.fail(f"Ollama对话测试失败: {e}")
    
    def test_ollama_text_analysis(self, ai_loader):
        """测试Ollama文本分析"""
        try:
            text = "今天天气很好，阳光明媚，适合出门散步。我感到心情愉快，工作效率也很高。"
            
            result = ai_loader.process_ai_request(
                AIServiceType.TEXT_ANALYSIS,
                text,
                {
                    'provider': 'ollama',
                    'temperature': 0.3,
                    'max_tokens': 200
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"Ollama文本分析失败: {result.data.get('error')}"
            assert result.data['type'] == 'text_analysis'
            assert result.data['provider'] == 'ollama'
            assert 'analysis' in result.data
            
            print(f"✅ Ollama文本分析成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   原文: {text}")
            print(f"   分析结果: {result.data['analysis']}")
            
        except Exception as e:
            pytest.fail(f"Ollama文本分析测试失败: {e}")
    
    def test_ollama_summarization(self, ai_loader):
        """测试Ollama文本摘要"""
        try:
            long_text = """
            人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，
            并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、
            语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，
            应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
            人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、
            也可能超过人的智能。在机器学习领域，深度学习技术取得了突破性进展，推动了AI技术的快速发展。
            """
            
            result = ai_loader.process_ai_request(
                AIServiceType.SUMMARIZATION,
                long_text.strip(),
                {
                    'provider': 'ollama',
                    'temperature': 0.3,
                    'max_tokens': 100
                }
            )
            
            # 验证结果
            assert 'error' not in result.data, f"Ollama文本摘要失败: {result.data.get('error')}"
            assert result.data['type'] == 'summarization'
            assert result.data['provider'] == 'ollama'
            assert 'summary' in result.data
            
            print(f"✅ Ollama文本摘要成功:")
            print(f"   模型: {result.data['model']}")
            print(f"   原文长度: {len(long_text)} 字符")
            print(f"   摘要: {result.data['summary']}")
            print(f"   压缩比: {result.data.get('compression_ratio', 'N/A')}")
            
        except Exception as e:
            pytest.fail(f"Ollama文本摘要测试失败: {e}")
    
    def test_ollama_template_integration(self, template_engine):
        """测试Ollama模板引擎集成"""
        try:
            template = """
            {%- set greeting = sd.ai('text_generation', '用一句话问候用户', provider='ollama', max_tokens=50) -%}
            {%- set analysis = sd.ai('text_analysis', '今天是个美好的一天，充满了希望和机遇', provider='ollama', max_tokens=100) -%}
            
            Ollama AI服务模板集成测试:
            - 问候语: {{ greeting.data.generated_text }}
            - 分析结果: {{ analysis.data.analysis }}
            - 使用的提供者: {{ greeting.data.provider }}
            - 使用的模型: {{ greeting.data.model }}
            """
            
            result = template_engine.render_template(template)
            
            # 验证结果
            assert "Ollama AI服务模板集成测试:" in result
            assert "ollama" in result.lower()
            assert "gemma" in result.lower() or "llama" in result.lower() or "mistral" in result.lower()
            
            print(f"✅ Ollama模板引擎集成成功:")
            print(result.strip())
            
        except Exception as e:
            pytest.fail(f"Ollama模板引擎集成测试失败: {e}")
    
    def test_ollama_async_processing(self, ai_loader):
        """测试Ollama异步处理"""
        async def async_test():
            # 异步处理单个请求
            result = await ai_loader.process_ai_request_async(
                AIServiceType.TEXT_GENERATION,
                "异步测试：请说一句鼓励的话",
                {'provider': 'ollama', 'max_tokens': 80}
            )
            
            assert 'error' not in result.data
            assert len(result.data.get('generated_text', '')) > 0
            
            # 异步批量处理
            requests = [
                {
                    'service_type': AIServiceType.TEXT_GENERATION,
                    'data': '生成一个创意标题',
                    'options': {'provider': 'ollama', 'max_tokens': 50}
                },
                {
                    'service_type': AIServiceType.TEXT_GENERATION,
                    'data': '写一句感谢的话',
                    'options': {'provider': 'ollama', 'max_tokens': 50}
                }
            ]
            
            results = await ai_loader.batch_process_async(requests)
            assert len(results) == 2
            
            for i, result in enumerate(results):
                assert 'error' not in result.data, f"批量请求 {i+1} 失败: {result.data.get('error')}"
            
            print(f"✅ Ollama异步处理成功:")
            print(f"   单个请求: {result.data.get('generated_text', '')}")
            print(f"   批量请求: {len(results)} 个请求全部成功")
            
            return True
        
        try:
            success = asyncio.run(async_test())
            assert success
        except Exception as e:
            pytest.fail(f"Ollama异步处理测试失败: {e}")
    
    def test_ollama_performance(self, ai_loader):
        """测试Ollama性能"""
        import time
        
        try:
            start_time = time.time()
            
            result = ai_loader.process_ai_request(
                AIServiceType.TEXT_GENERATION,
                "性能测试：请简单回答什么是AI",
                {
                    'provider': 'ollama',
                    'max_tokens': 50,
                    'temperature': 0.5
                }
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            assert 'error' not in result.data
            
            # 计算性能指标
            generated_text = result.data.get('generated_text', '')
            tokens_generated = len(generated_text.split())
            tokens_per_second = tokens_generated / duration if duration > 0 else 0
            
            print(f"✅ Ollama性能测试:")
            print(f"   响应时间: {duration:.2f} 秒")
            print(f"   生成tokens: {tokens_generated}")
            print(f"   生成速度: {tokens_per_second:.2f} tokens/秒")
            print(f"   模型: {result.data.get('model', 'N/A')}")
            
            # 性能断言（根据本地硬件调整）
            assert duration < 30, f"响应时间过长: {duration:.2f}秒"
            assert tokens_generated > 0, "没有生成任何内容"
            
        except Exception as e:
            pytest.fail(f"Ollama性能测试失败: {e}")


def test_show_ollama_setup_instructions():
    """显示Ollama设置说明"""
    print("\n" + "="*60)
    print("🦙 Ollama本地大模型设置说明:")
    print("="*60)
    print("""
1. 安装Ollama:
   - 访问: https://ollama.ai/
   - 下载并安装适合你系统的版本

2. 启动Ollama服务:
   ollama serve

3. 下载gemma3:4b模型:
   ollama pull gemma3:4b

4. 验证安装:
   ollama list

5. 其他推荐模型:
   ollama pull llama3:8b      # 更大更强的模型
   ollama pull mistral:7b     # 另一个优秀模型
   ollama pull codellama:7b   # 代码专用模型

6. 检查服务状态:
   curl http://localhost:11434/api/tags

注意: gemma3:4b模型大约需要2.5GB存储空间
""")
    print("="*60)


if __name__ == "__main__":
    # 首先显示设置说明
    test_show_ollama_setup_instructions()
    
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
