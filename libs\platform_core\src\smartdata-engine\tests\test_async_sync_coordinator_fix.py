#!/usr/bin/env python3
"""
AsyncSyncCoordinator修复验证测试

验证修复后的协调器能正确处理异步/同步调用
"""

import pytest
import sys
import os
import asyncio
import json
import tempfile
from pathlib import Path

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.async_sync_coordinator import AsyncSyncCoordinator
from plugins.file_loader.smart_file_loader import SmartFileLoader
from plugins.ai.smart_ai_loader import SmartAILoader
from plugins.ai.ai_factory import AIServiceType


class TestAsyncSyncCoordinatorFix:
    """AsyncSyncCoordinator修复验证测试类"""
    
    @pytest.fixture
    def coordinator(self):
        """创建协调器"""
        return AsyncSyncCoordinator(enable_debug=True)
    
    @pytest.fixture
    def test_file(self):
        """创建测试文件"""
        temp_dir = tempfile.mkdtemp()
        test_data = {"message": "协调器测试", "success": True}
        
        test_file = Path(temp_dir) / "test_data.json"
        test_file.write_text(json.dumps(test_data, ensure_ascii=False), encoding='utf-8')
        
        return str(test_file)
    
    def test_coordinator_sync_context(self, coordinator):
        """测试协调器在同步上下文中的行为"""
        
        # 定义测试函数
        def sync_func(x):
            return f"sync_result_{x}"
        
        async def async_func(x):
            await asyncio.sleep(0.01)  # 模拟异步操作
            return f"async_result_{x}"
        
        # 测试同步函数在同步上下文
        result1 = coordinator.smart_call(sync_func, "test1")
        assert result1 == "sync_result_test1"
        print(f"✅ 同步函数在同步上下文: {result1}")
        
        # 测试异步函数在同步上下文
        result2 = coordinator.smart_call(async_func, "test2")
        assert result2 == "async_result_test2"
        print(f"✅ 异步函数在同步上下文: {result2}")
    
    async def test_coordinator_async_context(self, coordinator):
        """测试协调器在异步上下文中的行为"""
        
        # 定义测试函数
        def sync_func(x):
            return f"sync_result_{x}"
        
        async def async_func(x):
            await asyncio.sleep(0.01)  # 模拟异步操作
            return f"async_result_{x}"
        
        # 测试同步函数在异步上下文
        result1 = coordinator.smart_call(sync_func, "test1")
        assert result1 == "sync_result_test1"
        print(f"✅ 同步函数在异步上下文: {result1}")
        
        # 测试异步函数在异步上下文
        result2 = coordinator.smart_call(async_func, "test2")
        assert result2 == "async_result_test2"
        print(f"✅ 异步函数在异步上下文: {result2}")
    
    def test_file_loader_sync_usage(self, test_file):
        """测试文件加载器的同步使用"""
        loader = SmartFileLoader(enable_debug=True)
        
        # 同步调用
        result = loader.load_file(test_file)
        
        # 验证结果
        assert hasattr(result, 'data'), "结果应该有data属性"
        assert 'error' not in result.data, f"不应该有错误: {result.data.get('error')}"
        assert result.data['data']['message'] == "协调器测试"
        
        print(f"✅ 文件加载器同步使用成功: {result.data['data']['message']}")
    
    async def test_file_loader_async_usage(self, test_file):
        """测试文件加载器的异步使用"""
        loader = SmartFileLoader(enable_debug=True)
        
        # 异步调用
        result = await loader.load_file_async(test_file)
        
        # 验证结果
        assert hasattr(result, 'data'), "结果应该有data属性"
        assert 'error' not in result.data, f"不应该有错误: {result.data.get('error')}"
        assert result.data['data']['message'] == "协调器测试"
        
        print(f"✅ 文件加载器异步使用成功: {result.data['data']['message']}")
    
    def test_ai_loader_sync_usage(self):
        """测试AI加载器的同步使用"""
        loader = SmartAILoader(enable_debug=True)
        
        # 配置演示模式
        loader.configure_provider('demo', {'enable_mock': True})
        
        # 同步调用
        result = loader.process_ai_request(
            AIServiceType.TEXT_GENERATION,
            "测试协调器",
            {'provider': 'demo'}
        )
        
        # 验证结果
        assert hasattr(result, 'data'), "结果应该有data属性"
        assert result.data['type'] == 'text_generation'
        
        print(f"✅ AI加载器同步使用成功: {result.data['type']}")
    
    async def test_ai_loader_async_usage(self):
        """测试AI加载器的异步使用"""
        loader = SmartAILoader(enable_debug=True)
        
        # 配置演示模式
        loader.configure_provider('demo', {'enable_mock': True})
        
        # 异步调用
        result = await loader.process_ai_request_async(
            AIServiceType.TEXT_GENERATION,
            "测试协调器",
            {'provider': 'demo'}
        )
        
        # 验证结果
        assert hasattr(result, 'data'), "结果应该有data属性"
        assert result.data['type'] == 'text_generation'
        
        print(f"✅ AI加载器异步使用成功: {result.data['type']}")
    
    def test_mixed_sync_async_calls(self, test_file):
        """测试混合同步/异步调用"""
        
        async def mixed_test():
            # 在异步上下文中进行各种调用
            
            # 1. 文件加载器同步调用
            file_loader = SmartFileLoader(enable_debug=True)
            file_result = file_loader.load_file(test_file)
            assert hasattr(file_result, 'data')
            print(f"✅ 异步上下文中的文件同步调用成功")
            
            # 2. 文件加载器异步调用
            file_result_async = await file_loader.load_file_async(test_file)
            assert hasattr(file_result_async, 'data')
            print(f"✅ 异步上下文中的文件异步调用成功")
            
            # 3. AI加载器同步调用
            ai_loader = SmartAILoader(enable_debug=True)
            ai_loader.configure_provider('demo', {'enable_mock': True})
            
            ai_result = ai_loader.process_ai_request(
                AIServiceType.TEXT_GENERATION,
                "测试",
                {'provider': 'demo'}
            )
            assert hasattr(ai_result, 'data')
            print(f"✅ 异步上下文中的AI同步调用成功")
            
            # 4. AI加载器异步调用
            ai_result_async = await ai_loader.process_ai_request_async(
                AIServiceType.TEXT_GENERATION,
                "测试",
                {'provider': 'demo'}
            )
            assert hasattr(ai_result_async, 'data')
            print(f"✅ 异步上下文中的AI异步调用成功")
            
            return True
        
        # 运行混合测试
        success = asyncio.run(mixed_test())
        assert success
        print(f"✅ 混合同步/异步调用测试通过")
    
    def test_coordinator_error_handling(self, coordinator):
        """测试协调器的错误处理"""
        
        def error_sync_func():
            raise ValueError("同步函数错误")
        
        async def error_async_func():
            await asyncio.sleep(0.01)
            raise ValueError("异步函数错误")
        
        # 测试同步函数错误
        with pytest.raises(ValueError, match="同步函数错误"):
            coordinator.smart_call(error_sync_func)
        print(f"✅ 同步函数错误处理正确")
        
        # 测试异步函数错误
        with pytest.raises(ValueError, match="异步函数错误"):
            coordinator.smart_call(error_async_func)
        print(f"✅ 异步函数错误处理正确")
    
    def test_coordinator_performance(self, coordinator):
        """测试协调器性能"""
        import time
        
        def fast_sync_func(x):
            return x * 2
        
        async def fast_async_func(x):
            return x * 2
        
        # 测试同步函数性能
        start_time = time.time()
        for i in range(100):
            result = coordinator.smart_call(fast_sync_func, i)
            assert result == i * 2
        sync_duration = time.time() - start_time
        
        # 测试异步函数性能
        start_time = time.time()
        for i in range(100):
            result = coordinator.smart_call(fast_async_func, i)
            assert result == i * 2
        async_duration = time.time() - start_time
        
        print(f"✅ 性能测试:")
        print(f"   同步函数100次调用: {sync_duration:.3f}秒")
        print(f"   异步函数100次调用: {async_duration:.3f}秒")
        
        # 异步函数调用应该不会太慢（考虑到线程池开销）
        assert async_duration < 10.0, f"异步函数调用过慢: {async_duration}秒"


def test_run_async_tests():
    """运行异步测试"""
    async def run_all_async_tests():
        coordinator = AsyncSyncCoordinator(enable_debug=True)
        test_instance = TestAsyncSyncCoordinatorFix()
        
        # 运行异步测试
        await test_instance.test_coordinator_async_context(coordinator)
        
        # 创建测试文件
        temp_dir = tempfile.mkdtemp()
        test_data = {"message": "协调器测试", "success": True}
        test_file = Path(temp_dir) / "test_data.json"
        test_file.write_text(json.dumps(test_data, ensure_ascii=False), encoding='utf-8')
        
        await test_instance.test_file_loader_async_usage(str(test_file))
        await test_instance.test_ai_loader_async_usage()
        
        # 清理
        test_file.unlink()
        temp_dir.rmdir() if temp_dir.exists() else None
        
        print("✅ 所有异步测试通过")
        return True
    
    success = asyncio.run(run_all_async_tests())
    assert success


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
