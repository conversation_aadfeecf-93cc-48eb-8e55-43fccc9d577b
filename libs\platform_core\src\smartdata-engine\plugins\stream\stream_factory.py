"""
Stream插件工厂模式实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现工厂模式
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union

from .stream_handler import (
    IStreamHandler, BaseStreamHandler, WebSocketHandler, SSEHandler,
    TCPHandler, UDPHandler, GRPCHandler, MQTTHandler
)


class StreamHandlerFactory:
    """Stream处理器工厂 - 按照插件标准规范"""
    
    # 注册的处理器类型
    _handlers: Dict[str, Type[IStreamHandler]] = {
        'websocket': WebSocketHandler,
        'sse': SSEHandler,
        'tcp': TCPHandler,
        'udp': UDPHandler,
        'grpc': GRPCHandler,
        'mqtt': MQTTHandler,
    }
    
    # 处理器优先级（数字越大优先级越高）
    _handler_priorities: Dict[str, int] = {
        'grpc': 90,         # gRPC最高优先级
        'websocket': 80,    # WebSocket次之
        'mqtt': 70,         # MQTT
        'sse': 60,          # SSE
        'tcp': 50,          # TCP
        'udp': 40,          # UDP最低优先级
    }
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.StreamHandlerFactory")
        self.enable_debug = enable_debug
        self._handler_cache: Dict[str, IStreamHandler] = {}
    
    @classmethod
    def register_handler(cls, handler_type: str, handler_class: Type[IStreamHandler], priority: int = 50):
        """注册新的处理器类型"""
        cls._handlers[handler_type] = handler_class
        cls._handler_priorities[handler_type] = priority
        logging.getLogger(__name__).info(f"注册Stream处理器: {handler_type} (优先级: {priority})")
    
    @classmethod
    def unregister_handler(cls, handler_type: str):
        """注销处理器类型"""
        if handler_type in cls._handlers:
            del cls._handlers[handler_type]
            cls._handler_priorities.pop(handler_type, None)
            logging.getLogger(__name__).info(f"注销Stream处理器: {handler_type}")
    
    @classmethod
    def get_registered_handlers(cls) -> Dict[str, Type[IStreamHandler]]:
        """获取所有注册的处理器"""
        return cls._handlers.copy()
    
    def detect_handler_type(self, data: Any) -> Optional[str]:
        """智能检测最适合的处理器类型"""
        try:
            # 按优先级排序处理器
            sorted_handlers = sorted(
                self._handlers.items(),
                key=lambda x: self._handler_priorities.get(x[0], 0),
                reverse=True
            )
            
            # 逐个检查处理器是否可以处理数据
            for handler_type, handler_class in sorted_handlers:
                try:
                    # 创建临时实例进行检测
                    temp_handler = handler_class(enable_debug=False)
                    if temp_handler.can_handle(data):
                        self.logger.debug(f"检测到处理器类型: {handler_type}")
                        return handler_type
                except Exception as e:
                    self.logger.debug(f"处理器 {handler_type} 检测失败: {e}")
                    continue
            
            self.logger.warning(f"未找到合适的处理器类型: {type(data)}")
            return None
            
        except Exception as e:
            self.logger.error(f"处理器类型检测失败: {e}")
            return None
    
    def create_handler(self, handler_type: str) -> Optional[IStreamHandler]:
        """创建指定类型的处理器"""
        try:
            if handler_type not in self._handlers:
                self.logger.error(f"未知的处理器类型: {handler_type}")
                return None
            
            # 检查缓存
            if handler_type in self._handler_cache:
                return self._handler_cache[handler_type]
            
            # 创建新的处理器实例
            handler_class = self._handlers[handler_type]
            handler = handler_class(enable_debug=self.enable_debug)
            
            # 缓存处理器实例
            self._handler_cache[handler_type] = handler
            
            self.logger.debug(f"创建Stream处理器: {handler_type}")
            return handler
            
        except Exception as e:
            self.logger.error(f"创建处理器失败: {handler_type}, 错误: {e}")
            return None
    
    def create_best_handler(self, data: Any) -> Optional[IStreamHandler]:
        """创建最适合的处理器"""
        handler_type = self.detect_handler_type(data)
        if handler_type:
            return self.create_handler(handler_type)
        return None
    
    def get_handler_info(self, handler_type: str) -> Optional[Dict[str, Any]]:
        """获取处理器信息"""
        try:
            if handler_type not in self._handlers:
                return None
            
            handler_class = self._handlers[handler_type]
            temp_handler = handler_class(enable_debug=False)
            
            return {
                'type': handler_type,
                'class_name': handler_class.__name__,
                'supported_types': temp_handler.get_supported_types(),
                'priority': self._handler_priorities.get(handler_type, 0),
                'description': handler_class.__doc__ or f"{handler_type} 处理器"
            }
            
        except Exception as e:
            self.logger.error(f"获取处理器信息失败: {handler_type}, 错误: {e}")
            return None
    
    def list_all_handlers(self) -> List[Dict[str, Any]]:
        """列出所有处理器信息"""
        handlers_info = []
        
        for handler_type in self._handlers:
            info = self.get_handler_info(handler_type)
            if info:
                handlers_info.append(info)
        
        # 按优先级排序
        handlers_info.sort(key=lambda x: x['priority'], reverse=True)
        return handlers_info
    
    def clear_cache(self):
        """清理处理器缓存"""
        self._handler_cache.clear()
        self.logger.debug("Stream处理器缓存已清理")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取工厂统计信息"""
        return {
            'registered_handlers': len(self._handlers),
            'cached_handlers': len(self._handler_cache),
            'handler_types': list(self._handlers.keys()),
            'priorities': self._handler_priorities.copy()
        }


class StreamConnectionFactory:
    """Stream连接工厂"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.StreamConnectionFactory")
        self._connection_pool: Dict[str, Any] = {}
    
    def create_connection(self, config: Dict[str, Any]) -> Optional[Any]:
        """创建Stream连接"""
        try:
            protocol = config.get('protocol', 'websocket')
            connection_key = self._generate_connection_key(config)
            
            # 检查连接池
            if connection_key in self._connection_pool:
                return self._connection_pool[connection_key]
            
            # 根据协议创建不同类型的连接配置
            if protocol == 'websocket':
                connection_config = self._create_websocket_config(config)
            elif protocol == 'sse':
                connection_config = self._create_sse_config(config)
            elif protocol == 'tcp':
                connection_config = self._create_tcp_config(config)
            elif protocol == 'udp':
                connection_config = self._create_udp_config(config)
            elif protocol == 'grpc':
                connection_config = self._create_grpc_config(config)
            elif protocol == 'mqtt':
                connection_config = self._create_mqtt_config(config)
            else:
                self.logger.warning(f"不支持的协议: {protocol}")
                return None
            
            # 缓存连接配置
            self._connection_pool[connection_key] = connection_config
            
            self.logger.debug(f"创建Stream连接配置: {connection_key}")
            return connection_config
            
        except Exception as e:
            self.logger.error(f"创建Stream连接失败: {e}")
            return None
    
    def _create_websocket_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建WebSocket连接配置"""
        return {
            'protocol': 'websocket',
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 8080),
            'path': config.get('path', '/ws'),
            'ssl': config.get('enable_ssl', False),
            'compression': config.get('enable_compression', True),
            'ping_interval': config.get('ping_interval', 30),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _create_sse_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建SSE连接配置"""
        return {
            'protocol': 'sse',
            'url': config.get('url', 'http://localhost:8080/events'),
            'reconnect_interval': config.get('reconnect_interval', 5),
            'max_reconnect_attempts': config.get('max_reconnect_attempts', 10),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _create_tcp_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建TCP连接配置"""
        return {
            'protocol': 'tcp',
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 9999),
            'buffer_size': config.get('buffer_size', 8192),
            'keepalive': config.get('keepalive', True),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _create_udp_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建UDP连接配置"""
        return {
            'protocol': 'udp',
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 9999),
            'max_packet_size': config.get('max_packet_size', 65507),
            'broadcast': config.get('broadcast', False),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _create_grpc_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建gRPC连接配置"""
        return {
            'protocol': 'grpc',
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 50051),
            'compression': config.get('compression', 'gzip'),
            'max_message_length': config.get('max_message_length', 4 * 1024 * 1024),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _create_mqtt_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """创建MQTT连接配置"""
        return {
            'protocol': 'mqtt',
            'host': config.get('host', 'localhost'),
            'port': config.get('port', 1883),
            'client_id': config.get('client_id', 'smartdata_client'),
            'qos': config.get('qos', 1),
            'retain': config.get('retain', False),
            'clean_session': config.get('clean_session', True),
            'timeout': config.get('timeout', 30.0)
        }
    
    def _generate_connection_key(self, config: Dict[str, Any]) -> str:
        """生成连接键"""
        protocol = config.get('protocol', 'websocket')
        host = config.get('host', 'localhost')
        port = config.get('port', 8080)
        return f"{protocol}://{host}:{port}"
    
    async def close_all_connections(self):
        """关闭所有连接"""
        for connection_key, connection in self._connection_pool.items():
            try:
                # 如果是实际的连接对象，关闭它
                if hasattr(connection, 'close'):
                    await connection.close()
                self.logger.debug(f"关闭Stream连接: {connection_key}")
            except Exception as e:
                self.logger.error(f"关闭连接失败: {connection_key}, 错误: {e}")
        
        self._connection_pool.clear()


# 全局工厂实例
stream_handler_factory = StreamHandlerFactory()
stream_connection_factory = StreamConnectionFactory()


def get_stream_handler_factory() -> StreamHandlerFactory:
    """获取Stream处理器工厂实例"""
    return stream_handler_factory


def get_stream_connection_factory() -> StreamConnectionFactory:
    """获取Stream连接工厂实例"""
    return stream_connection_factory
