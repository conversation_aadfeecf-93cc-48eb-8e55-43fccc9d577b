"""
Stream插件标准处理器接口实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现IPluginHandler接口
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urlparse

from core.smart_data_object import SmartDataObject
from core.async_sync_coordinator import AsyncSyncCoordinator


class IStreamHandler(ABC):
    """Stream处理器接口 - 按照插件标准规范"""
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        pass
    
    @abstractmethod
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理数据"""
        pass
    
    @abstractmethod
    def get_handler_type(self) -> str:
        """获取处理器类型"""
        pass
    
    @abstractmethod
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        pass


class BaseStreamHandler(IStreamHandler):
    """Stream处理器基类"""
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self._cache: Dict[str, Any] = {}
        
    def can_handle(self, data: Any) -> bool:
        """基础的数据类型检查"""
        if isinstance(data, str):
            # 检查是否是流URL
            return any(protocol in data.lower() for protocol in ['ws://', 'wss://', 'tcp://', 'udp://'])
        elif isinstance(data, dict):
            # 检查是否包含流配置
            return any(key in data for key in ['protocol', 'stream', 'websocket', 'sse'])
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['stream', 'websocket', 'sse', 'tcp', 'udp']


class WebSocketHandler(BaseStreamHandler):
    """WebSocket处理器"""
    
    def get_handler_type(self) -> str:
        return "websocket"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理WebSocket"""
        if isinstance(data, str):
            return data.startswith(('ws://', 'wss://'))
        elif isinstance(data, dict):
            return data.get('protocol') == 'websocket' or 'websocket' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['websocket', 'ws', 'wss', 'real_time']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理WebSocket连接"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # WebSocket特殊配置
            config = options or {}
            config.setdefault('protocol', 'websocket')
            config.setdefault('enable_compression', True)
            config.setdefault('ping_interval', 30)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"WebSocket处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class SSEHandler(BaseStreamHandler):
    """Server-Sent Events处理器"""
    
    def get_handler_type(self) -> str:
        return "sse"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理SSE"""
        if isinstance(data, str):
            return '/events' in data or 'text/event-stream' in data
        elif isinstance(data, dict):
            return data.get('protocol') == 'sse' or 'sse' in data or 'event_stream' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['sse', 'event_stream', 'server_sent_events']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理SSE连接"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # SSE特殊配置
            config = options or {}
            config.setdefault('protocol', 'sse')
            config.setdefault('reconnect_interval', 5)
            config.setdefault('max_reconnect_attempts', 10)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"SSE处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class TCPHandler(BaseStreamHandler):
    """TCP流处理器"""
    
    def get_handler_type(self) -> str:
        return "tcp"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理TCP"""
        if isinstance(data, str):
            return data.startswith('tcp://')
        elif isinstance(data, dict):
            return data.get('protocol') == 'tcp' or 'tcp' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['tcp', 'tcp_stream', 'socket']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理TCP连接"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # TCP特殊配置
            config = options or {}
            config.setdefault('protocol', 'tcp')
            config.setdefault('buffer_size', 8192)
            config.setdefault('keepalive', True)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"TCP处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class UDPHandler(BaseStreamHandler):
    """UDP流处理器"""
    
    def get_handler_type(self) -> str:
        return "udp"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理UDP"""
        if isinstance(data, str):
            return data.startswith('udp://')
        elif isinstance(data, dict):
            return data.get('protocol') == 'udp' or 'udp' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['udp', 'udp_stream', 'datagram']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理UDP连接"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # UDP特殊配置
            config = options or {}
            config.setdefault('protocol', 'udp')
            config.setdefault('max_packet_size', 65507)
            config.setdefault('broadcast', False)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"UDP处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class GRPCHandler(BaseStreamHandler):
    """gRPC流处理器"""
    
    def get_handler_type(self) -> str:
        return "grpc"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理gRPC"""
        if isinstance(data, dict):
            return data.get('protocol') == 'grpc' or 'grpc' in data or 'proto' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['grpc', 'grpc_stream', 'proto', 'protobuf']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理gRPC流"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # gRPC特殊配置
            config = options or {}
            config.setdefault('protocol', 'grpc')
            config.setdefault('compression', 'gzip')
            config.setdefault('max_message_length', 4 * 1024 * 1024)  # 4MB
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"gRPC处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })


class MQTTHandler(BaseStreamHandler):
    """MQTT流处理器"""
    
    def get_handler_type(self) -> str:
        return "mqtt"
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理MQTT"""
        if isinstance(data, str):
            return data.startswith('mqtt://')
        elif isinstance(data, dict):
            return data.get('protocol') == 'mqtt' or 'mqtt' in data or 'topic' in data
        return False
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        return ['mqtt', 'mqtt_stream', 'topic', 'publish', 'subscribe']
    
    async def process(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """处理MQTT连接"""
        try:
            from .stream_processor import StreamDataProcessor
            
            processor = StreamDataProcessor(enable_debug=True)
            
            # MQTT特殊配置
            config = options or {}
            config.setdefault('protocol', 'mqtt')
            config.setdefault('qos', 1)
            config.setdefault('retain', False)
            config.setdefault('clean_session', True)
            
            await processor.configure(config)
            await processor.open()
            
            try:
                result = await processor.process(data)
                return SmartDataObject(result)
            finally:
                await processor.close()
                
        except Exception as e:
            self.logger.error(f"MQTT处理失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'handler_type': self.get_handler_type(),
                'success': False
            })
