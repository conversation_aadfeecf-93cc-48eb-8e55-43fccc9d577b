"""
REST插件工厂模式实现

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现工厂模式
"""

import logging
from typing import Any, Dict, List, Optional, Type, Union
from urllib.parse import urlparse

from .rest_handler import (
    IRestHandler, BaseRestHandler, HttpGetHandler, HttpPostHandler,
    ApiEndpointHandler, RestfulApiHandler, GraphQLHandler
)


class RestHandlerFactory:
    """REST处理器工厂 - 按照插件标准规范"""
    
    # 注册的处理器类型
    _handlers: Dict[str, Type[IRestHandler]] = {
        'http_get': HttpGetHandler,
        'http_post': HttpPostHandler,
        'api_endpoint': ApiEndpointHandler,
        'restful_api': RestfulApiHandler,
        'graphql': GraphQLHandler,
    }
    
    # 处理器优先级（数字越大优先级越高）
    _handler_priorities: Dict[str, int] = {
        'graphql': 90,          # GraphQL最高优先级
        'restful_api': 80,      # RESTful API次之
        'api_endpoint': 70,     # API端点
        'http_post': 60,        # HTTP POST
        'http_get': 50,         # HTTP GET最低优先级
    }
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.RestHandlerFactory")
        self.enable_debug = enable_debug
        self._handler_cache: Dict[str, IRestHandler] = {}
    
    @classmethod
    def register_handler(cls, handler_type: str, handler_class: Type[IRestHandler], priority: int = 50):
        """注册新的处理器类型"""
        cls._handlers[handler_type] = handler_class
        cls._handler_priorities[handler_type] = priority
        logging.getLogger(__name__).info(f"注册REST处理器: {handler_type} (优先级: {priority})")
    
    @classmethod
    def unregister_handler(cls, handler_type: str):
        """注销处理器类型"""
        if handler_type in cls._handlers:
            del cls._handlers[handler_type]
            cls._handler_priorities.pop(handler_type, None)
            logging.getLogger(__name__).info(f"注销REST处理器: {handler_type}")
    
    @classmethod
    def get_registered_handlers(cls) -> Dict[str, Type[IRestHandler]]:
        """获取所有注册的处理器"""
        return cls._handlers.copy()
    
    def detect_handler_type(self, data: Any) -> Optional[str]:
        """智能检测最适合的处理器类型"""
        try:
            # 按优先级排序处理器
            sorted_handlers = sorted(
                self._handlers.items(),
                key=lambda x: self._handler_priorities.get(x[0], 0),
                reverse=True
            )
            
            # 逐个检查处理器是否可以处理数据
            for handler_type, handler_class in sorted_handlers:
                try:
                    # 创建临时实例进行检测
                    temp_handler = handler_class(enable_debug=False)
                    if temp_handler.can_handle(data):
                        self.logger.debug(f"检测到处理器类型: {handler_type}")
                        return handler_type
                except Exception as e:
                    self.logger.debug(f"处理器 {handler_type} 检测失败: {e}")
                    continue
            
            self.logger.warning(f"未找到合适的处理器类型: {type(data)}")
            return None
            
        except Exception as e:
            self.logger.error(f"处理器类型检测失败: {e}")
            return None
    
    def create_handler(self, handler_type: str) -> Optional[IRestHandler]:
        """创建指定类型的处理器"""
        try:
            if handler_type not in self._handlers:
                self.logger.error(f"未知的处理器类型: {handler_type}")
                return None
            
            # 检查缓存
            if handler_type in self._handler_cache:
                return self._handler_cache[handler_type]
            
            # 创建新的处理器实例
            handler_class = self._handlers[handler_type]
            handler = handler_class(enable_debug=self.enable_debug)
            
            # 缓存处理器实例
            self._handler_cache[handler_type] = handler
            
            self.logger.debug(f"创建REST处理器: {handler_type}")
            return handler
            
        except Exception as e:
            self.logger.error(f"创建处理器失败: {handler_type}, 错误: {e}")
            return None
    
    def create_best_handler(self, data: Any) -> Optional[IRestHandler]:
        """创建最适合的处理器"""
        handler_type = self.detect_handler_type(data)
        if handler_type:
            return self.create_handler(handler_type)
        return None
    
    def get_handler_info(self, handler_type: str) -> Optional[Dict[str, Any]]:
        """获取处理器信息"""
        try:
            if handler_type not in self._handlers:
                return None
            
            handler_class = self._handlers[handler_type]
            temp_handler = handler_class(enable_debug=False)
            
            return {
                'type': handler_type,
                'class_name': handler_class.__name__,
                'supported_types': temp_handler.get_supported_types(),
                'priority': self._handler_priorities.get(handler_type, 0),
                'description': handler_class.__doc__ or f"{handler_type} 处理器"
            }
            
        except Exception as e:
            self.logger.error(f"获取处理器信息失败: {handler_type}, 错误: {e}")
            return None
    
    def list_all_handlers(self) -> List[Dict[str, Any]]:
        """列出所有处理器信息"""
        handlers_info = []
        
        for handler_type in self._handlers:
            info = self.get_handler_info(handler_type)
            if info:
                handlers_info.append(info)
        
        # 按优先级排序
        handlers_info.sort(key=lambda x: x['priority'], reverse=True)
        return handlers_info
    
    def clear_cache(self):
        """清理处理器缓存"""
        self._handler_cache.clear()
        self.logger.debug("REST处理器缓存已清理")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取工厂统计信息"""
        return {
            'registered_handlers': len(self._handlers),
            'cached_handlers': len(self._handler_cache),
            'handler_types': list(self._handlers.keys()),
            'priorities': self._handler_priorities.copy()
        }


class RestConnectionFactory:
    """REST连接工厂"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.RestConnectionFactory")
        self._connection_pool: Dict[str, Any] = {}
    
    def create_connection(self, config: Dict[str, Any]) -> Optional[Any]:
        """创建REST连接"""
        try:
            # 这里可以根据配置创建不同类型的连接
            # 例如：httpx.AsyncClient, aiohttp.ClientSession等
            
            connection_key = self._generate_connection_key(config)
            
            # 检查连接池
            if connection_key in self._connection_pool:
                return self._connection_pool[connection_key]
            
            # 创建新连接（这里使用httpx作为示例）
            try:
                import httpx
                
                client_config = {
                    'timeout': config.get('timeout', 30.0),
                    'verify': config.get('verify_ssl', True),
                    'follow_redirects': config.get('follow_redirects', True),
                }
                
                if 'base_url' in config:
                    client_config['base_url'] = config['base_url']
                
                connection = httpx.AsyncClient(**client_config)
                self._connection_pool[connection_key] = connection
                
                self.logger.debug(f"创建REST连接: {connection_key}")
                return connection
                
            except ImportError:
                self.logger.warning("httpx未安装，无法创建连接")
                return None
            
        except Exception as e:
            self.logger.error(f"创建REST连接失败: {e}")
            return None
    
    def _generate_connection_key(self, config: Dict[str, Any]) -> str:
        """生成连接键"""
        key_parts = [
            config.get('base_url', ''),
            str(config.get('timeout', 30.0)),
            str(config.get('verify_ssl', True)),
        ]
        return '|'.join(key_parts)
    
    async def close_all_connections(self):
        """关闭所有连接"""
        for connection_key, connection in self._connection_pool.items():
            try:
                if hasattr(connection, 'aclose'):
                    await connection.aclose()
                elif hasattr(connection, 'close'):
                    await connection.close()
                self.logger.debug(f"关闭REST连接: {connection_key}")
            except Exception as e:
                self.logger.error(f"关闭连接失败: {connection_key}, 错误: {e}")
        
        self._connection_pool.clear()


# 全局工厂实例
rest_handler_factory = RestHandlerFactory()
rest_connection_factory = RestConnectionFactory()


def get_rest_handler_factory() -> RestHandlerFactory:
    """获取REST处理器工厂实例"""
    return rest_handler_factory


def get_rest_connection_factory() -> RestConnectionFactory:
    """获取REST连接工厂实例"""
    return rest_connection_factory
