#!/usr/bin/env python3
"""
真实场景演示

演示File Loader和AI插件在真实场景中的使用
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# 添加路径
sys.path.insert(0, '.')

from template import create_template_engine
from plugins.plugin_registry import PluginRegistry
from plugins.file_loader.smart_file_loader import global_file_loader
from plugins.ai.smart_ai_loader import global_ai_loader
from plugins.ai.ai_factory import AIServiceType


def create_demo_files():
    """创建演示文件"""
    temp_dir = tempfile.mkdtemp()
    print(f"📁 创建演示文件目录: {temp_dir}")
    
    # 1. JSON数据文件
    user_data = {
        "users": [
            {"id": 1, "name": "张三", "age": 28, "department": "技术部", "skills": ["Python", "AI", "数据分析"]},
            {"id": 2, "name": "李四", "age": 32, "department": "产品部", "skills": ["产品设计", "用户研究"]},
            {"id": 3, "name": "王五", "age": 26, "department": "技术部", "skills": ["JavaScript", "前端开发"]},
            {"id": 4, "name": "赵六", "age": 30, "department": "市场部", "skills": ["市场营销", "数据分析"]}
        ]
    }
    
    json_file = Path(temp_dir) / "company_users.json"
    json_file.write_text(json.dumps(user_data, ensure_ascii=False, indent=2), encoding='utf-8')
    
    # 2. CSV报告文件
    csv_data = """项目名称,状态,负责人,完成度,备注
智能客服系统,进行中,张三,75%,AI技术应用
用户体验优化,已完成,李四,100%,用户满意度提升20%
前端重构项目,计划中,王五,10%,使用最新技术栈
市场推广活动,进行中,赵六,60%,多渠道推广策略"""
    
    csv_file = Path(temp_dir) / "project_report.csv"
    csv_file.write_text(csv_data, encoding='utf-8')
    
    # 3. 配置文件
    config_data = """
# 公司配置文件
company:
  name: "智能科技有限公司"
  founded: 2020
  employees: 150
  
departments:
  - name: "技术部"
    head: "张三"
    budget: 500000
  - name: "产品部"
    head: "李四"
    budget: 300000
  - name: "市场部"
    head: "赵六"
    budget: 200000

ai_services:
  enabled: true
  providers: ["openai", "claude"]
  budget_monthly: 1000
"""
    
    yaml_file = Path(temp_dir) / "company_config.yaml"
    yaml_file.write_text(config_data, encoding='utf-8')
    
    # 4. 长文本文件
    long_text = """
人工智能技术发展报告

随着人工智能技术的快速发展，我们公司在AI领域取得了显著进展。本报告总结了过去一年的主要成就和未来规划。

主要成就：
1. 成功部署了智能客服系统，客户满意度提升了20%
2. 开发了基于机器学习的数据分析平台，提高了决策效率
3. 实施了自然语言处理技术，优化了文档处理流程
4. 建立了AI研发团队，拥有10名专业工程师

技术栈：
- 机器学习框架：TensorFlow, PyTorch
- 自然语言处理：BERT, GPT系列模型
- 数据处理：Pandas, NumPy, Spark
- 云服务：AWS, Azure, 阿里云

未来规划：
1. 扩大AI团队规模，计划招聘20名AI工程师
2. 投资研发更先进的深度学习模型
3. 探索多模态AI技术应用
4. 建立AI伦理和安全标准

挑战与机遇：
虽然AI技术发展迅速，但我们也面临着数据隐私、算法偏见、技术人才短缺等挑战。
同时，AI技术的普及也为我们带来了巨大的市场机遇。

结论：
人工智能将继续是我们公司的核心战略方向，我们将持续投入资源，
确保在AI技术竞争中保持领先地位。
"""
    
    txt_file = Path(temp_dir) / "ai_report.txt"
    txt_file.write_text(long_text.strip(), encoding='utf-8')
    
    return {
        'temp_dir': temp_dir,
        'json_file': str(json_file),
        'csv_file': str(csv_file),
        'yaml_file': str(yaml_file),
        'txt_file': str(txt_file)
    }


def demo_file_processing(files):
    """演示文件处理功能"""
    print("\n🔧 演示文件处理功能")
    print("=" * 40)
    
    # 1. 处理JSON文件
    print("\n📄 处理JSON用户数据:")
    json_result = global_file_loader.load_file(files['json_file'])
    if 'error' not in json_result.data:
        users = json_result.data['data']['users']
        print(f"   ✅ 成功加载 {len(users)} 个用户")
        print(f"   📊 技术部员工: {len([u for u in users if u['department'] == '技术部'])} 人")
    else:
        print(f"   ❌ 加载失败: {json_result.data['error']}")
    
    # 2. 处理CSV文件
    print("\n📊 处理CSV项目报告:")
    csv_result = global_file_loader.load_file(files['csv_file'])
    if 'error' not in csv_result.data:
        projects = csv_result.data['data']
        print(f"   ✅ 成功加载 {len(projects)} 个项目")
        completed = len([p for p in projects if p['状态'] == '已完成'])
        print(f"   📈 已完成项目: {completed} 个")
    else:
        print(f"   ❌ 加载失败: {csv_result.data['error']}")
    
    # 3. 处理YAML配置
    print("\n⚙️  处理YAML配置文件:")
    yaml_result = global_file_loader.load_file(files['yaml_file'])
    if 'error' not in yaml_result.data:
        config = yaml_result.data['data']
        print(f"   ✅ 成功加载配置")
        print(f"   🏢 公司名称: {config.get('company', {}).get('name', 'N/A')}")
        print(f"   👥 员工数量: {config.get('company', {}).get('employees', 'N/A')}")
    else:
        print(f"   ❌ 加载失败: {yaml_result.data['error']}")
    
    # 4. 处理文本文件
    print("\n📝 处理文本报告:")
    txt_result = global_file_loader.load_file(files['txt_file'])
    if 'error' not in txt_result.data:
        content = txt_result.data['content']
        lines = txt_result.data['lines']
        print(f"   ✅ 成功加载文本")
        print(f"   📏 文本长度: {len(content)} 字符")
        print(f"   📄 行数: {len(lines)} 行")
    else:
        print(f"   ❌ 加载失败: {txt_result.data['error']}")
    
    return {
        'json_result': json_result,
        'csv_result': csv_result,
        'yaml_result': yaml_result,
        'txt_result': txt_result
    }


def demo_ai_processing(file_results):
    """演示AI处理功能"""
    print("\n🤖 演示AI处理功能")
    print("=" * 40)
    
    # 配置演示模式（如果没有真实API Key）
    if not (os.getenv('OPENAI_API_KEY') or os.getenv('ANTHROPIC_API_KEY')):
        print("   ℹ️  使用演示模式（模拟AI响应）")
        global_ai_loader.configure_provider('demo', {'enable_mock': True})
    
    # 1. 文本摘要
    if 'error' not in file_results['txt_result'].data:
        print("\n📝 AI文本摘要:")
        content = file_results['txt_result'].data['content']
        
        summary_result = global_ai_loader.process_ai_request(
            AIServiceType.SUMMARIZATION,
            content,
            {'provider': 'openai', 'max_tokens': 200}
        )
        
        if 'error' not in summary_result.data:
            print(f"   ✅ 摘要生成成功")
            print(f"   📄 原文长度: {len(content)} 字符")
            summary = summary_result.data.get('summary', summary_result.data.get('generated_text', ''))
            print(f"   📋 摘要: {summary[:100]}...")
        else:
            print(f"   ❌ 摘要失败: {summary_result.data['error']}")
    
    # 2. 数据分析
    if 'error' not in file_results['json_result'].data:
        print("\n📊 AI数据分析:")
        users = file_results['json_result'].data['data']['users']
        
        analysis_prompt = f"分析以下员工数据，给出洞察和建议：{json.dumps(users, ensure_ascii=False)}"
        
        analysis_result = global_ai_loader.process_ai_request(
            AIServiceType.TEXT_ANALYSIS,
            analysis_prompt,
            {'provider': 'claude', 'max_tokens': 300}
        )
        
        if 'error' not in analysis_result.data:
            print(f"   ✅ 数据分析成功")
            analysis = analysis_result.data.get('generated_text', analysis_result.data.get('response', {}).get('content', ''))
            print(f"   🔍 分析结果: {analysis[:150]}...")
        else:
            print(f"   ❌ 分析失败: {analysis_result.data['error']}")
    
    # 3. 对话交互
    print("\n💬 AI对话交互:")
    messages = [
        {"role": "user", "content": "根据我们公司的项目报告，你认为哪个项目最值得关注？"}
    ]
    
    conversation_result = global_ai_loader.process_ai_request(
        AIServiceType.CONVERSATION,
        messages,
        {'provider': 'openai', 'max_tokens': 200}
    )
    
    if 'error' not in conversation_result.data:
        print(f"   ✅ 对话成功")
        response = conversation_result.data.get('response', {}).get('content', conversation_result.data.get('generated_text', ''))
        print(f"   🤖 AI回复: {response[:150]}...")
    else:
        print(f"   ❌ 对话失败: {conversation_result.data['error']}")


def demo_template_integration(files):
    """演示模板引擎集成"""
    print("\n🎨 演示模板引擎集成")
    print("=" * 40)
    
    # 创建模板引擎
    registry = PluginRegistry()
    engine = create_template_engine(registry, debug=True)
    
    # 标准化文件路径用于模板
    json_path = files['json_file'].replace('\\', '/')
    csv_path = files['csv_file'].replace('\\', '/')
    txt_path = files['txt_file'].replace('\\', '/')
    
    # 复杂的模板示例
    template = f"""
# 📊 智能数据分析报告

## 1. 员工数据分析
{{%- set users_data = sd.file('{json_path}') -%}}
{{%- set users = users_data.data.data.users -%}}

- 总员工数: {{{{ users|length }}}} 人
- 技术部员工: {{{{ users|selectattr('department', 'equalto', '技术部')|list|length }}}} 人
- 平均年龄: {{{{ (users|sum(attribute='age') / users|length)|round(1) }}}} 岁

## 2. 项目进展分析
{{%- set projects_data = sd.file('{csv_path}') -%}}
{{%- set projects = projects_data.data.data -%}}

- 总项目数: {{{{ projects|length }}}} 个
- 已完成项目: {{{{ projects|selectattr('状态', 'equalto', '已完成')|list|length }}}} 个
- 进行中项目: {{{{ projects|selectattr('状态', 'equalto', '进行中')|list|length }}}} 个

## 3. AI智能摘要
{{%- set report_data = sd.file('{txt_path}') -%}}
{{%- set summary = sd.ai('summarization', report_data.data.content, provider='openai', max_tokens=150) -%}}

**AI生成摘要:**
{{{{ summary.data.summary or summary.data.generated_text or '摘要生成失败' }}}}

## 4. AI建议
{{%- set suggestion = sd.ai('text_generation', '基于以上数据，给出3条管理建议', provider='claude', max_tokens=200) -%}}

**AI管理建议:**
{{{{ suggestion.data.generated_text or suggestion.data.response.content or '建议生成失败' }}}}

---
*报告生成时间: {{{{ moment().format('YYYY-MM-DD HH:mm:ss') }}}}*
"""
    
    try:
        print("\n🎯 渲染智能分析报告...")
        result = engine.render_template(template)
        
        print("✅ 模板渲染成功!")
        print("\n📋 生成的报告:")
        print("-" * 50)
        print(result.strip())
        print("-" * 50)
        
    except Exception as e:
        print(f"❌ 模板渲染失败: {e}")


def demo_path_handling():
    """演示路径处理功能"""
    print("\n🛣️  演示路径处理功能")
    print("=" * 40)
    
    # 创建测试文件
    temp_dir = tempfile.mkdtemp()
    test_file = Path(temp_dir) / "path_test.json"
    test_data = {"message": "路径处理测试", "timestamp": "2024-01-01"}
    test_file.write_text(json.dumps(test_data, ensure_ascii=False), encoding='utf-8')
    
    # 测试不同路径格式
    path_variants = [
        str(test_file),                           # 标准路径
        str(test_file).replace('\\', '/'),        # 正斜杠
        str(test_file).replace('\\', '\\\\'),     # 双反斜杠
    ]
    
    print(f"\n📁 测试文件: {test_file}")
    
    for i, path_variant in enumerate(path_variants, 1):
        print(f"\n🔍 测试路径格式 {i}: {path_variant}")
        
        try:
            result = global_file_loader.load_file(path_variant)
            if 'error' not in result.data:
                print(f"   ✅ 加载成功: {result.data['data']['message']}")
            else:
                print(f"   ❌ 加载失败: {result.data['error']}")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    # 清理
    try:
        test_file.unlink()
        Path(temp_dir).rmdir()
    except:
        pass


def main():
    """主演示函数"""
    print("🚀 SmartData Engine 真实场景演示")
    print("=" * 50)
    
    try:
        # 1. 创建演示文件
        files = create_demo_files()
        
        # 2. 演示路径处理
        demo_path_handling()
        
        # 3. 演示文件处理
        file_results = demo_file_processing(files)
        
        # 4. 演示AI处理
        demo_ai_processing(file_results)
        
        # 5. 演示模板集成
        demo_template_integration(files)
        
        print("\n🎉 演示完成!")
        print("\n📋 演示总结:")
        print("   ✅ 文件路径标准化 - 支持多种路径格式")
        print("   ✅ 多格式文件处理 - JSON, CSV, YAML, TXT")
        print("   ✅ AI智能处理 - 摘要, 分析, 对话")
        print("   ✅ 模板引擎集成 - 无缝数据和AI集成")
        print("   ✅ 异步优先架构 - 高性能处理")
        
        # 清理演示文件
        import shutil
        try:
            shutil.rmtree(files['temp_dir'])
            print(f"\n🧹 已清理演示文件: {files['temp_dir']}")
        except:
            pass
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
