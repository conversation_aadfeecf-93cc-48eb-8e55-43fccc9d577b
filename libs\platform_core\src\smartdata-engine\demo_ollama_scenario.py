#!/usr/bin/env python3
"""
Ollama本地大模型真实场景演示

使用本地Ollama gemma3:4b模型进行完整的AI插件演示
"""

import os
import sys
import json
import tempfile
import asyncio
import aiohttp
from pathlib import Path

# 添加路径
sys.path.insert(0, '.')

from template import create_template_engine
from plugins.plugin_registry import PluginRegistry
from plugins.file_loader.smart_file_loader import global_file_loader
from plugins.ai.smart_ai_loader import global_ai_loader
from plugins.ai.ai_factory import AIServiceType


async def check_ollama_service():
    """检查Ollama服务状态"""
    print("🔍 检查Ollama服务状态...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:11434/api/tags', timeout=aiohttp.ClientTimeout(total=5)) as response:
                if response.status == 200:
                    data = await response.json()
                    models = [m['name'] for m in data.get('models', [])]
                    
                    print(f"✅ Ollama服务运行正常")
                    print(f"📋 可用模型: {models}")
                    
                    if 'gemma3:4b' in models:
                        print(f"🎯 找到目标模型: gemma3:4b")
                        return 'gemma3:4b'
                    elif models:
                        print(f"⚠️  未找到gemma3:4b，使用第一个可用模型: {models[0]}")
                        return models[0]
                    else:
                        print("❌ 没有可用的模型")
                        return None
                else:
                    print(f"❌ Ollama API返回错误: {response.status}")
                    return None
    except Exception as e:
        print(f"❌ 无法连接到Ollama服务: {e}")
        print("\n💡 请确保:")
        print("   1. Ollama已安装并运行: ollama serve")
        print("   2. 已下载模型: ollama pull gemma3:4b")
        return None


def create_demo_files():
    """创建演示文件"""
    temp_dir = tempfile.mkdtemp()
    print(f"📁 创建演示文件目录: {temp_dir}")
    
    # 1. 公司员工数据
    employee_data = {
        "company": "智能科技有限公司",
        "employees": [
            {
                "id": 1,
                "name": "张三",
                "position": "AI工程师",
                "department": "技术部",
                "skills": ["Python", "机器学习", "深度学习"],
                "performance": "优秀",
                "feedback": "工作积极主动，技术能力强，团队合作良好"
            },
            {
                "id": 2,
                "name": "李四",
                "position": "产品经理",
                "department": "产品部",
                "skills": ["产品设计", "用户研究", "数据分析"],
                "performance": "良好",
                "feedback": "产品思维清晰，用户导向明确，需要加强技术理解"
            },
            {
                "id": 3,
                "name": "王五",
                "position": "前端开发",
                "department": "技术部",
                "skills": ["JavaScript", "React", "Vue"],
                "performance": "优秀",
                "feedback": "前端技术扎实，界面设计美观，响应速度快"
            }
        ]
    }
    
    json_file = Path(temp_dir) / "employees.json"
    json_file.write_text(json.dumps(employee_data, ensure_ascii=False, indent=2), encoding='utf-8')
    
    # 2. 项目报告
    project_report = """
# AI项目进展报告

## 项目概述
我们公司正在开发一个智能客服系统，该系统基于大语言模型技术，能够理解用户问题并提供准确的回答。

## 技术架构
- 前端：React + TypeScript
- 后端：Python + FastAPI
- AI模型：基于Transformer架构的大语言模型
- 数据库：PostgreSQL + Redis
- 部署：Docker + Kubernetes

## 当前进展
1. 模型训练已完成，准确率达到92%
2. 前端界面开发完成80%
3. 后端API开发完成90%
4. 系统集成测试进行中

## 面临挑战
1. 模型推理速度需要进一步优化
2. 多轮对话上下文理解有待改进
3. 特定领域知识覆盖不够全面

## 下一步计划
1. 优化模型推理性能，目标响应时间<2秒
2. 增强多轮对话能力
3. 扩充领域知识库
4. 进行用户测试和反馈收集

## 预期成果
预计该系统上线后能够处理80%的常见客服问题，大幅提升客服效率和用户满意度。
"""
    
    txt_file = Path(temp_dir) / "project_report.txt"
    txt_file.write_text(project_report.strip(), encoding='utf-8')
    
    # 3. 配置文件
    config_data = """
# 系统配置
system:
  name: "智能客服系统"
  version: "1.0.0"
  environment: "development"

ai_model:
  name: "gemma3:4b"
  provider: "ollama"
  temperature: 0.7
  max_tokens: 500

database:
  host: "localhost"
  port: 5432
  name: "customer_service"

performance:
  target_response_time: 2.0
  max_concurrent_users: 1000
  cache_ttl: 3600
"""
    
    yaml_file = Path(temp_dir) / "system_config.yaml"
    yaml_file.write_text(config_data, encoding='utf-8')
    
    return {
        'temp_dir': temp_dir,
        'json_file': str(json_file),
        'txt_file': str(txt_file),
        'yaml_file': str(yaml_file)
    }


def demo_file_processing(files):
    """演示文件处理功能"""
    print("\n🔧 演示文件处理功能")
    print("=" * 40)
    
    # 处理员工数据
    print("\n👥 处理员工数据:")
    json_result = global_file_loader.load_file(files['json_file'])
    if 'error' not in json_result.data:
        employees = json_result.data['data']['employees']
        print(f"   ✅ 成功加载 {len(employees)} 名员工")
        tech_employees = [e for e in employees if e['department'] == '技术部']
        print(f"   💻 技术部员工: {len(tech_employees)} 人")
    else:
        print(f"   ❌ 加载失败: {json_result.data['error']}")
    
    # 处理项目报告
    print("\n📊 处理项目报告:")
    txt_result = global_file_loader.load_file(files['txt_file'])
    if 'error' not in txt_result.data:
        content = txt_result.data['content']
        print(f"   ✅ 成功加载报告")
        print(f"   📏 报告长度: {len(content)} 字符")
        print(f"   📄 行数: {len(txt_result.data['lines'])} 行")
    else:
        print(f"   ❌ 加载失败: {txt_result.data['error']}")
    
    # 处理配置文件
    print("\n⚙️  处理配置文件:")
    yaml_result = global_file_loader.load_file(files['yaml_file'])
    if 'error' not in yaml_result.data:
        print(f"   ✅ 成功加载配置")
        # 注意：这里可能是文本格式，不是解析后的YAML
        print(f"   📋 配置内容已加载")
    else:
        print(f"   ❌ 加载失败: {yaml_result.data['error']}")
    
    return {
        'json_result': json_result,
        'txt_result': txt_result,
        'yaml_result': yaml_result
    }


async def demo_ollama_ai_processing(file_results, model):
    """演示Ollama AI处理功能"""
    print(f"\n🦙 演示Ollama AI处理功能 (模型: {model})")
    print("=" * 50)
    
    # 配置Ollama提供者
    global_ai_loader.configure_provider('ollama', {
        'base_url': 'http://localhost:11434',
        'default_model': model
    })
    
    # 1. 文本生成
    print("\n✨ AI文本生成:")
    try:
        generation_result = await global_ai_loader.process_ai_request_async(
            AIServiceType.TEXT_GENERATION,
            "请为我们的智能客服系统写一句宣传标语",
            {
                'provider': 'ollama',
                'temperature': 0.8,
                'max_tokens': 100
            }
        )
        
        if 'error' not in generation_result.data:
            print(f"   ✅ 生成成功")
            print(f"   💡 标语: {generation_result.data['generated_text']}")
        else:
            print(f"   ❌ 生成失败: {generation_result.data['error']}")
    except Exception as e:
        print(f"   ❌ 生成异常: {e}")
    
    # 2. 文本摘要
    if 'error' not in file_results['txt_result'].data:
        print("\n📝 AI文本摘要:")
        try:
            content = file_results['txt_result'].data['content']
            summary_result = await global_ai_loader.process_ai_request_async(
                AIServiceType.SUMMARIZATION,
                content,
                {
                    'provider': 'ollama',
                    'temperature': 0.3,
                    'max_tokens': 150
                }
            )
            
            if 'error' not in summary_result.data:
                print(f"   ✅ 摘要生成成功")
                print(f"   📋 摘要: {summary_result.data['summary']}")
                print(f"   📊 压缩比: {summary_result.data.get('compression_ratio', 'N/A'):.2%}")
            else:
                print(f"   ❌ 摘要失败: {summary_result.data['error']}")
        except Exception as e:
            print(f"   ❌ 摘要异常: {e}")
    
    # 3. 员工数据分析
    if 'error' not in file_results['json_result'].data:
        print("\n👥 AI员工数据分析:")
        try:
            employees = file_results['json_result'].data['data']['employees']
            analysis_prompt = f"请分析以下员工数据，给出团队优势和改进建议：\n{json.dumps(employees, ensure_ascii=False, indent=2)}"
            
            analysis_result = await global_ai_loader.process_ai_request_async(
                AIServiceType.TEXT_ANALYSIS,
                analysis_prompt,
                {
                    'provider': 'ollama',
                    'temperature': 0.5,
                    'max_tokens': 200
                }
            )
            
            if 'error' not in analysis_result.data:
                print(f"   ✅ 分析完成")
                print(f"   🔍 分析结果: {analysis_result.data['analysis']}")
            else:
                print(f"   ❌ 分析失败: {analysis_result.data['error']}")
        except Exception as e:
            print(f"   ❌ 分析异常: {e}")
    
    # 4. 对话交互
    print("\n💬 AI对话交互:")
    try:
        messages = [
            {"role": "user", "content": "基于我们的项目报告，你认为我们的AI客服系统有什么优势？"}
        ]
        
        conversation_result = await global_ai_loader.process_ai_request_async(
            AIServiceType.CONVERSATION,
            messages,
            {
                'provider': 'ollama',
                'temperature': 0.7,
                'max_tokens': 200
            }
        )
        
        if 'error' not in conversation_result.data:
            print(f"   ✅ 对话成功")
            print(f"   🤖 AI回复: {conversation_result.data['response']['content']}")
        else:
            print(f"   ❌ 对话失败: {conversation_result.data['error']}")
    except Exception as e:
        print(f"   ❌ 对话异常: {e}")


def demo_ollama_template_integration(files, model):
    """演示Ollama模板引擎集成"""
    print(f"\n🎨 演示Ollama模板引擎集成 (模型: {model})")
    print("=" * 50)
    
    # 创建模板引擎
    registry = PluginRegistry()
    engine = create_template_engine(registry, debug=True)
    
    # 配置全局AI加载器
    global_ai_loader.configure_provider('ollama', {
        'base_url': 'http://localhost:11434',
        'default_model': model
    })
    
    # 标准化文件路径
    json_path = files['json_file'].replace('\\', '/')
    txt_path = files['txt_file'].replace('\\', '/')
    
    # 复杂的模板示例
    template = f"""
# 🤖 Ollama AI智能分析报告

## 1. 员工数据智能分析
{{%- set employees_data = sd.file('{json_path}') -%}}
{{%- set employees = employees_data.data.data.employees -%}}

### 基础统计
- 总员工数: {{{{ employees|length }}}} 人
- 技术部员工: {{{{ employees|selectattr('department', 'equalto', '技术部')|list|length }}}} 人
- 优秀员工: {{{{ employees|selectattr('performance', 'equalto', '优秀')|list|length }}}} 人

### AI智能洞察
{{%- set team_analysis = sd.ai('text_analysis', '分析团队结构：技术部' + (employees|selectattr('department', 'equalto', '技术部')|list|length|string) + '人，产品部' + (employees|selectattr('department', 'equalto', '产品部')|list|length|string) + '人，整体表现优秀', provider='ollama', max_tokens=150) -%}}

**团队分析结果:**
{{{{ team_analysis.data.analysis }}}}

## 2. 项目报告智能摘要
{{%- set report_data = sd.file('{txt_path}') -%}}
{{%- set summary = sd.ai('summarization', report_data.data.content, provider='ollama', max_tokens=120) -%}}

**AI生成摘要:**
{{{{ summary.data.summary }}}}

## 3. AI建议生成
{{%- set suggestion = sd.ai('text_generation', '基于以上团队和项目分析，给出3条具体的管理建议', provider='ollama', max_tokens=180) -%}}

**AI管理建议:**
{{{{ suggestion.data.generated_text }}}}

## 4. 技术信息
- 使用模型: {{{{ summary.data.model }}}}
- AI提供者: {{{{ summary.data.provider }}}}
- 处理时间: 实时生成

---
*本报告由Ollama本地大模型 {model} 智能生成*
"""
    
    try:
        print("\n🎯 渲染Ollama智能分析报告...")
        result = engine.render_template(template)
        
        print("✅ 模板渲染成功!")
        print("\n📋 生成的智能报告:")
        print("-" * 60)
        print(result.strip())
        print("-" * 60)
        
    except Exception as e:
        print(f"❌ 模板渲染失败: {e}")
        import traceback
        traceback.print_exc()


async def demo_ollama_performance(model):
    """演示Ollama性能测试"""
    print(f"\n⚡ Ollama性能测试 (模型: {model})")
    print("=" * 40)
    
    import time
    
    # 配置AI加载器
    global_ai_loader.configure_provider('ollama', {
        'base_url': 'http://localhost:11434',
        'default_model': model
    })
    
    # 性能测试用例
    test_cases = [
        {
            'name': '短文本生成',
            'service': AIServiceType.TEXT_GENERATION,
            'data': '请说一句鼓励的话',
            'options': {'max_tokens': 30}
        },
        {
            'name': '中等文本生成',
            'service': AIServiceType.TEXT_GENERATION,
            'data': '请写一段关于人工智能发展的描述',
            'options': {'max_tokens': 100}
        },
        {
            'name': '对话回复',
            'service': AIServiceType.CONVERSATION,
            'data': [{'role': 'user', 'content': '你好，请介绍一下自己'}],
            'options': {'max_tokens': 80}
        }
    ]
    
    print(f"\n📊 性能测试结果:")
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            start_time = time.time()
            
            result = await global_ai_loader.process_ai_request_async(
                test_case['service'],
                test_case['data'],
                {
                    'provider': 'ollama',
                    'temperature': 0.7,
                    **test_case['options']
                }
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if 'error' not in result.data:
                # 计算生成的token数
                generated_text = result.data.get('generated_text', result.data.get('response', {}).get('content', ''))
                tokens_generated = len(generated_text.split())
                tokens_per_second = tokens_generated / duration if duration > 0 else 0
                
                print(f"   {i}. {test_case['name']}:")
                print(f"      ⏱️  响应时间: {duration:.2f}秒")
                print(f"      📝 生成tokens: {tokens_generated}")
                print(f"      🚀 生成速度: {tokens_per_second:.1f} tokens/秒")
                print(f"      💬 内容预览: {generated_text[:50]}...")
            else:
                print(f"   {i}. {test_case['name']}: ❌ 失败 - {result.data['error']}")
                
        except Exception as e:
            print(f"   {i}. {test_case['name']}: ❌ 异常 - {e}")
        
        print()


async def main():
    """主演示函数"""
    print("🦙 Ollama本地大模型真实场景演示")
    print("=" * 50)
    
    try:
        # 1. 检查Ollama服务
        model = await check_ollama_service()
        if not model:
            print("\n❌ Ollama服务不可用，演示终止")
            return
        
        # 2. 创建演示文件
        files = create_demo_files()
        
        # 3. 演示文件处理
        file_results = demo_file_processing(files)
        
        # 4. 演示Ollama AI处理
        await demo_ollama_ai_processing(file_results, model)
        
        # 5. 演示模板集成
        demo_ollama_template_integration(files, model)
        
        # 6. 性能测试
        await demo_ollama_performance(model)
        
        print("\n🎉 Ollama演示完成!")
        print("\n📋 演示总结:")
        print("   ✅ Ollama服务连接 - 本地大模型运行正常")
        print("   ✅ 文件处理集成 - 多格式文件智能加载")
        print("   ✅ AI智能处理 - 文本生成、摘要、分析、对话")
        print("   ✅ 模板引擎集成 - 无缝AI和数据集成")
        print("   ✅ 性能测试 - 本地推理性能评估")
        print(f"   🦙 使用模型: {model}")
        
        # 清理演示文件
        import shutil
        try:
            shutil.rmtree(files['temp_dir'])
            print(f"\n🧹 已清理演示文件: {files['temp_dir']}")
        except:
            pass
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
