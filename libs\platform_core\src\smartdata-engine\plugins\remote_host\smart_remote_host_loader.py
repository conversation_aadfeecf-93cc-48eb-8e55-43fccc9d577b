"""
Remote Host插件智能加载器

按照PLUGIN_STANDARDS_SPECIFICATION.md标准实现智能加载器
"""

import asyncio
import logging
import time
from typing import Any, Dict, List, Optional, Union

from core.smart_data_object import SmartDataObject
from core.async_sync_coordinator import AsyncSyncCoordinator
from .remote_host_factory import get_remote_host_handler_factory, get_remote_host_connection_factory
from .remote_host_handler import IRemoteHostHandler


class SmartRemoteHostLoader:
    """Remote Host智能加载器 - 按照插件标准规范"""
    
    def __init__(self, enable_debug: bool = False):
        self.logger = logging.getLogger(f"{__name__}.SmartRemoteHostLoader")
        self.coordinator = AsyncSyncCoordinator(enable_debug=enable_debug)
        self.handler_factory = get_remote_host_handler_factory()
        self.connection_factory = get_remote_host_connection_factory()
        
        # 缓存和性能
        self._result_cache: Dict[str, Dict[str, Any]] = {}
        self._connection_cache: Dict[str, Any] = {}
        self._cache_ttl = 300  # 5分钟默认缓存
        self._performance_stats = {
            'total_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_execution_time': 0.0,
            'active_connections': 0
        }
        
        # 配置
        self._config = {
            'enable_cache': True,
            'cache_ttl': 300,
            'timeout': 30.0,
            'max_retries': 3,
            'retry_delay': 1.0,
            'enable_smart_detection': True,
            'enable_performance_tracking': True,
            'connection_pool_size': 10,
            'keepalive_interval': 30.0
        }
    
    def configure(self, **config):
        """配置加载器"""
        self._config.update(config)
        self._cache_ttl = self._config.get('cache_ttl', 300)
        self.logger.info(f"Remote Host智能加载器配置已更新: {config}")
    
    def load_data(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """同步加载数据"""
        return self.coordinator.smart_call(self.load_data_async, data, options)
    
    async def load_data_async(self, data: Any, options: Optional[Dict] = None) -> SmartDataObject:
        """异步加载数据"""
        start_time = time.time()
        
        try:
            self._performance_stats['total_operations'] += 1
            
            # 生成缓存键
            cache_key = self._generate_cache_key(data, options)
            
            # 检查缓存（对于非状态改变操作）
            if self._should_use_cache(data, options):
                if cache_key in self._result_cache:
                    cached_item = self._result_cache[cache_key]
                    if time.time() - cached_item['timestamp'] < self._cache_ttl:
                        self._performance_stats['cache_hits'] += 1
                        self.logger.debug(f"缓存命中: {cache_key}")
                        return SmartDataObject(cached_item['data'])
                    else:
                        # 缓存过期，删除
                        del self._result_cache[cache_key]
            
            self._performance_stats['cache_misses'] += 1
            
            # 智能检测处理器
            handler = self._detect_and_create_handler(data)
            if not handler:
                raise ValueError(f"无法找到合适的Remote Host处理器处理数据: {type(data)}")
            
            # 处理数据
            result = await handler.process(data, options)
            
            # 缓存结果（如果适用）
            if self._should_cache_result(data, options, result):
                self._cache_result(cache_key, result.data)
            
            # 更新性能统计
            execution_time = time.time() - start_time
            self._update_performance_stats(True, execution_time)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._update_performance_stats(False, execution_time)
            
            self.logger.error(f"Remote Host数据加载失败: {e}")
            return SmartDataObject({
                'error': str(e),
                'success': False,
                'loader': 'SmartRemoteHostLoader',
                'execution_time': execution_time
            })
    
    def _detect_and_create_handler(self, data: Any) -> Optional[IRemoteHostHandler]:
        """智能检测并创建处理器"""
        try:
            if self._config['enable_smart_detection']:
                # 使用工厂的智能检测
                handler = self.handler_factory.create_best_handler(data)
                if handler:
                    self.logger.debug(f"智能检测到处理器: {handler.get_handler_type()}")
                    return handler
            
            # 回退到默认处理器
            self.logger.debug("使用默认SSH连接处理器")
            return self.handler_factory.create_handler('ssh_connection')
            
        except Exception as e:
            self.logger.error(f"处理器检测失败: {e}")
            return None
    
    def _should_use_cache(self, data: Any, options: Optional[Dict] = None) -> bool:
        """判断是否应该使用缓存"""
        if not self._config['enable_cache']:
            return False
        
        # 对于状态改变操作，不使用缓存
        if isinstance(data, dict):
            state_changing_ops = ['upload', 'download', 'delete', 'create', 'modify', 'execute']
            if any(op in str(data).lower() for op in state_changing_ops):
                return False
        
        return True
    
    def _should_cache_result(self, data: Any, options: Optional[Dict], result: SmartDataObject) -> bool:
        """判断是否应该缓存结果"""
        if not self._config['enable_cache']:
            return False
        
        if not result.data.get('success', True):
            return False
        
        # 只缓存查询类操作的结果
        if isinstance(data, dict):
            query_ops = ['list', 'get', 'info', 'status', 'monitor']
            return any(op in str(data).lower() for op in query_ops)
        
        return False
    
    def _generate_cache_key(self, data: Any, options: Optional[Dict] = None) -> str:
        """生成缓存键"""
        try:
            key_parts = [str(data)]
            
            if options:
                # 只包含影响结果的选项
                cache_relevant_options = {
                    k: v for k, v in options.items()
                    if k in ['host', 'username', 'command', 'path', 'timeout']
                }
                if cache_relevant_options:
                    key_parts.append(str(sorted(cache_relevant_options.items())))
            
            return '|'.join(key_parts)
            
        except Exception as e:
            self.logger.warning(f"生成缓存键失败: {e}")
            return str(hash(str(data)))
    
    def _cache_result(self, cache_key: str, data: Any):
        """缓存结果"""
        try:
            # 限制缓存大小
            if len(self._result_cache) >= 500:  # Remote Host操作缓存较小
                # 删除最旧的缓存项
                oldest_key = min(self._result_cache.keys(), 
                               key=lambda k: self._result_cache[k]['timestamp'])
                del self._result_cache[oldest_key]
            
            self._result_cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.warning(f"缓存结果失败: {e}")
    
    def _update_performance_stats(self, success: bool, execution_time: float):
        """更新性能统计"""
        try:
            if success:
                self._performance_stats['successful_operations'] += 1
            else:
                self._performance_stats['failed_operations'] += 1
            
            # 更新平均执行时间
            total_ops = self._performance_stats['total_operations']
            current_avg = self._performance_stats['average_execution_time']
            new_avg = (current_avg * (total_ops - 1) + execution_time) / total_ops
            self._performance_stats['average_execution_time'] = new_avg
            
        except Exception as e:
            self.logger.warning(f"更新性能统计失败: {e}")
    
    def batch_load(self, data_list: List[Any], options: Optional[Dict] = None) -> List[SmartDataObject]:
        """批量加载数据（同步）"""
        return self.coordinator.smart_call(self.batch_load_async, data_list, options)
    
    async def batch_load_async(self, data_list: List[Any], options: Optional[Dict] = None) -> List[SmartDataObject]:
        """批量加载数据（异步）"""
        try:
            # 并发处理所有请求
            tasks = [self.load_data_async(data, options) for data in data_list]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    processed_results.append(SmartDataObject({
                        'error': str(result),
                        'success': False,
                        'index': i,
                        'loader': 'SmartRemoteHostLoader'
                    }))
                else:
                    processed_results.append(result)
            
            return processed_results
            
        except Exception as e:
            self.logger.error(f"批量加载失败: {e}")
            return [SmartDataObject({
                'error': str(e),
                'success': False,
                'loader': 'SmartRemoteHostLoader'
            }) for _ in data_list]
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self._performance_stats.copy()
        
        # 计算额外指标
        total_ops = stats['total_operations']
        if total_ops > 0:
            stats['success_rate'] = (stats['successful_operations'] / total_ops) * 100
            stats['failure_rate'] = (stats['failed_operations'] / total_ops) * 100
            stats['cache_hit_rate'] = (stats['cache_hits'] / total_ops) * 100
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['cache_hit_rate'] = 0.0
        
        stats['cache_size'] = len(self._result_cache)
        stats['connection_cache_size'] = len(self._connection_cache)
        stats['config'] = self._config.copy()
        
        return stats
    
    def clear_cache(self):
        """清理缓存"""
        self._result_cache.clear()
        self._connection_cache.clear()
        self.logger.info("Remote Host加载器缓存已清理")
    
    def reset_stats(self):
        """重置性能统计"""
        self._performance_stats = {
            'total_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_execution_time': 0.0,
            'active_connections': 0
        }
        self.logger.info("Remote Host加载器性能统计已重置")
    
    def get_supported_types(self) -> List[str]:
        """获取支持的数据类型"""
        supported_types = set()
        
        # 收集所有处理器支持的类型
        for handler_info in self.handler_factory.list_all_handlers():
            supported_types.update(handler_info['supported_types'])
        
        return list(supported_types)
    
    def can_handle(self, data: Any) -> bool:
        """检查是否可以处理数据"""
        try:
            handler = self.handler_factory.create_best_handler(data)
            return handler is not None
        except Exception:
            return False
    
    async def close(self):
        """关闭加载器"""
        try:
            await self.connection_factory.close_all_connections()
            self.clear_cache()
            self.logger.info("Remote Host智能加载器已关闭")
        except Exception as e:
            self.logger.error(f"关闭Remote Host智能加载器失败: {e}")


# 全局智能加载器实例
global_remote_host_loader = SmartRemoteHostLoader()


def get_smart_remote_host_loader() -> SmartRemoteHostLoader:
    """获取全局Remote Host智能加载器实例"""
    return global_remote_host_loader
