#!/usr/bin/env python3
"""
文件路径标准化测试

验证文件加载器能正确处理各种路径格式
"""

import pytest
import sys
import os
import tempfile
import json
from pathlib import Path

# 添加路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from plugins.file_loader.smart_file_loader import SmartFileLoader


class TestFilePathNormalization:
    """文件路径标准化测试类"""
    
    @pytest.fixture
    def test_file(self):
        """创建测试文件"""
        temp_dir = tempfile.mkdtemp()
        test_data = {"message": "路径标准化测试", "success": True}
        
        test_file = Path(temp_dir) / "test_data.json"
        test_file.write_text(json.dumps(test_data, ensure_ascii=False), encoding='utf-8')
        
        return str(test_file)
    
    @pytest.fixture
    def file_loader(self):
        """创建文件加载器"""
        return SmartFileLoader(enable_debug=True)
    
    def test_windows_path_normalization(self, file_loader, test_file):
        """测试Windows路径标准化"""
        # 获取原始路径
        original_path = test_file
        
        # 测试不同的路径格式
        path_variants = [
            original_path,                                    # 原始路径
            original_path.replace('\\', '/'),                # 正斜杠
            original_path.replace('\\', '\\\\'),             # 双反斜杠
            original_path.replace(os.sep, '\\'),             # 强制反斜杠
        ]
        
        results = []
        for path_variant in path_variants:
            try:
                result = file_loader.load_file(path_variant)
                results.append({
                    'path': path_variant,
                    'success': 'error' not in result.data,
                    'data': result.data
                })
            except Exception as e:
                results.append({
                    'path': path_variant,
                    'success': False,
                    'error': str(e)
                })
        
        # 验证所有路径变体都能成功加载
        for i, result in enumerate(results):
            print(f"路径变体 {i+1}: {result['path']}")
            print(f"  成功: {result['success']}")
            if result['success']:
                print(f"  数据: {result['data'].get('data', {}).get('message', 'N/A')}")
            else:
                print(f"  错误: {result.get('error', 'N/A')}")
            
            assert result['success'], f"路径变体 {i+1} 加载失败: {result.get('error', 'Unknown error')}"
            if result['success']:
                assert result['data']['data']['message'] == "路径标准化测试"
        
        print("✅ Windows路径标准化验证通过")
    
    def test_relative_path_handling(self, file_loader):
        """测试相对路径处理"""
        # 创建相对路径测试文件
        current_dir = os.getcwd()
        relative_dir = "temp_test_dir"
        relative_file = "relative_test.json"
        
        # 创建测试目录和文件
        full_dir = os.path.join(current_dir, relative_dir)
        os.makedirs(full_dir, exist_ok=True)
        
        test_data = {"type": "relative_path_test", "value": "success"}
        full_file_path = os.path.join(full_dir, relative_file)
        
        with open(full_file_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False)
        
        try:
            # 测试相对路径
            relative_path = os.path.join(relative_dir, relative_file)
            result = file_loader.load_file(relative_path)
            
            assert 'error' not in result.data, f"相对路径加载失败: {result.data.get('error')}"
            assert result.data['data']['type'] == "relative_path_test"
            
            print(f"✅ 相对路径处理验证通过: {relative_path}")
            
        finally:
            # 清理测试文件
            try:
                os.remove(full_file_path)
                os.rmdir(full_dir)
            except:
                pass
    
    def test_path_normalization_method(self, file_loader):
        """测试路径标准化方法"""
        test_cases = [
            # (输入路径, 期望包含的特征)
            ("C:\\Users\\<USER>\\file.txt", "file.txt"),
            ("C:/Users/<USER>/file.txt", "file.txt"),
            ("C:\\\\Users\\\\<USER>\\\\file.txt", "file.txt"),
            ("./relative/file.txt", "file.txt"),
            ("../parent/file.txt", "file.txt"),
            ("folder\\subfolder\\file.txt", "file.txt"),
            ("folder/subfolder/file.txt", "file.txt"),
        ]
        
        for input_path, expected_feature in test_cases:
            normalized = file_loader._normalize_file_path(input_path)
            
            # 验证标准化后的路径包含期望的特征
            assert expected_feature in normalized, f"标准化失败: {input_path} -> {normalized}"
            
            # 验证路径是绝对路径
            assert os.path.isabs(normalized), f"标准化后不是绝对路径: {normalized}"
            
            print(f"路径标准化: '{input_path}' -> '{normalized}'")
        
        print("✅ 路径标准化方法验证通过")
    
    def test_special_characters_in_path(self, file_loader):
        """测试路径中的特殊字符"""
        # 创建包含特殊字符的文件名
        temp_dir = tempfile.mkdtemp()
        special_chars_file = "测试文件_with-special@chars#123.json"
        
        test_data = {"special_chars": True, "filename": special_chars_file}
        full_path = os.path.join(temp_dir, special_chars_file)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False)
        
        try:
            # 测试加载包含特殊字符的文件
            result = file_loader.load_file(full_path)
            
            assert 'error' not in result.data, f"特殊字符文件加载失败: {result.data.get('error')}"
            assert result.data['data']['special_chars'] == True
            
            print(f"✅ 特殊字符路径处理验证通过: {special_chars_file}")
            
        finally:
            # 清理测试文件
            try:
                os.remove(full_path)
                os.rmdir(temp_dir)
            except:
                pass
    
    def test_template_integration_with_paths(self):
        """测试模板引擎中的路径处理"""
        from template import create_template_engine
        from plugins.plugin_registry import PluginRegistry
        
        # 创建测试文件
        temp_dir = tempfile.mkdtemp()
        test_file = os.path.join(temp_dir, "template_test.json")
        test_data = {"template_integration": True, "message": "模板路径测试"}
        
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, ensure_ascii=False)
        
        try:
            registry = PluginRegistry()
            engine = create_template_engine(registry, debug=True)
            
            # 测试不同路径格式在模板中的使用
            path_for_template = test_file.replace('\\', '/')  # 在模板中使用正斜杠
            
            template = f"""
            {{%- set data = sd.file('{path_for_template}') -%}}
            文件加载测试:
            - 类型: {{{{ data.data.type }}}}
            - 集成状态: {{{{ data.data.data.template_integration }}}}
            - 消息: {{{{ data.data.data.message }}}}
            """
            
            result = engine.render_template(template)
            
            # 验证模板渲染结果
            assert "类型: json" in result
            assert "集成状态: True" in result
            assert "消息: 模板路径测试" in result
            
            print("✅ 模板引擎路径处理验证通过")
            
        finally:
            # 清理测试文件
            try:
                os.remove(test_file)
                os.rmdir(temp_dir)
            except:
                pass


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v", "-s"])
